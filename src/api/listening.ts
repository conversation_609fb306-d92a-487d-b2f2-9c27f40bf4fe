/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
import api from '@/lib/api';
import { string } from 'zod';

export interface ListRequest {
  /**
   * 分页标记 (用于下一页)
   */
  pageToken?: string;
  /**
   * 每页数量，最大100
   */
  perPage?: number;
  /**
   * 过滤条件 0: 全部, 1: 未开始, 2: 已结束（纪要生成中和已结束）
   */
  status?: number;
  // 部门id
  deptId?: string;
  // 学生id
  studentId?: number;
  startTime?: string;
  endTime?: string;
}

/**
 * ListResponse
 */
export interface ListResponse {
  /**
   * 列表
   */
  list: ListeningInfo[];
  /**
   * 下一页的 token，如果为空表示没有更多
   */
  nextPageToken: string;
}

/**
 * CreateListeningReq
 */
export interface PostListenRequest {
  deptId?: string;
  studentId?: string;

  /**
   * 资料
   */
  materials?: ListeningMaterial[];

  fileUrl?: string;
}

/**
 * ListeningMaterial
 */
export interface ListeningMaterial {
  /**
   * 资料 id
   */
  id?: string;
  /**
   * 资料名称
   */
  name?: string;
  /**
   * 资料类型（1：图片，2：视频，3：音频，4：其他）
   */
  type?: number;
  /**
   * 资料 url
   */
  url?: string;
}

/**
 * ListeningInfo
 */
export interface ListeningInfo {
  /**
   * 会议 id
   */
  listeningId?: string;
  deptName?: string;
  startTime?: number;
  endTime?: number;

  /**
   * 状态 (1: 未开始, 2: 进行中, 3: 已取消, 4: 纪要生成中, 5: 已完成  6: 待指认，小组倾听模式下转写完成后需老师手动修改发言人后再生成倾听分析)
   */
  status?: number;
  material?: ListeningMaterial[];
  staffInfo?: UserInfo;
  studentInfo?: UserInfo;
  nextPageToken?: string;
  studentInfos?: UserInfo[];
  contextDescription?: string;
  /**
   * 倾听类型（1：一对一，2：小组）
   */
  type?: number;
}

export interface UserInfo {
  roleId?: string;
  roleName?: string;
}

/**
 * CreateListeningRsp
 */
export interface PostListenResponse {
  /**
   * 会议 id
   */
  listeningId?: string;
}

export interface RecordSummary {
  id?: number;
  content?: string;
}
/**
 * GetListeningRsp
 */
export interface ListenRecodeDetailResponse {
  listening?: ListeningInfo;
  summaryContents?: string;
  /**
   * 会议资料列表
   */
  materials?: ListeningMaterial[];
  /**
   * 会议 id
   */
  listeningId?: string;
  /**
   * 录音文件信息
   */
  recording?: RecordingInfo;
  /**
   * 语音识别技术方案（1: 通义听悟-实时, 2: 自建算法-离线）
   */
  voiceProvider?: number;
  segments?: TranscriptSegment[];
  /**
   * 倾听下一步计划
   */
  todos?: string;
  interpretationText?: string;
  narrativeAnalysis?: NarrativeAnalysis;
  tags?: {
    tagId: string;
    name: string;
  }[];
}
export interface NarrativeAnalysis {
  metrics: NarrativeAnalysisMetrics[];
}
export interface NarrativeAnalysisMetrics {
  code: string;
  name: string;
  score: number;
  subMetrics?: NarrativeAnalysisMetrics[];
}
export interface RecordingInfo {
  /**
   * 录音时长（秒）
   */
  duration?: string;
  /**
   * 录音文件格式
   */
  fileFormat?: string;
  /**
   * 大小 (bytes)
   */
  fileSize?: string;
  /**
   * 录音文件 url
   */
  fileUrl?: string;
  id?: string;
}
/**
 * UpdateListeningReq
 */
export interface UpdateListenRecodeRequest {
  /**
   * 会议描述
   */
  description?: string;
  /**
   * 会议地点
   */
  location?: string;
  /**
   * 会议资料列表
   */
  materials?: ListeningMaterial[];
  /**
   * 会议 ID
   */
  listeningId?: string;
  /**
   * 会议开始时间，单位：秒
   */
  startTime?: number;
  /**
   * 会议主题
   */
  subject?: string;
  /**
   * 除组织者外的最新参会人 id 列表，组织者无法删除
   */
  userIds?: string[];
}

export interface ExportListenRecodeRequest {
  /**
   * 导出类型
   */
  type: number; //1音频，2会议纪要，3原文
  /**
   * 会议id
   */
  listeningId: string;
  /**
   * 导出格式
   */
  format?: string;
  /**
   * 是否显示发言人 (默认 true)
   */
  showSpeaker?: boolean;
  /**
   * 是否显示时间戳 (默认 true)
   */
  showTimestamp?: boolean;
}
/**
 * ExportRsp
 */
export interface ExportListenRecodeResponse {
  /**
   * 建议的文件名
   */
  fileName?: string;
  /**
   * 导出的文件下载链接
   */
  fileUrl?: string;
}
/**
 * AddListeningMaterialReq
 */
export interface AddListenRecodeMaterialRequest {
  /**
   * 会议 ID
   */
  listeningId?: string;
  /**
   * 资料名称
   */
  name?: string;
  /**
   * 资料类型（1：图片，2：视频，3：音频，4：其他）
   */
  type?: number;
  /**
   * 资料 URL
   */
  url?: string;
}
/**
 * AddListeningMaterialRsp
 */
export interface AddListenRecodeMaterialResponse {
  /**
   * 新增资料的 ID
   */
  materialId?: string;
}
export interface DeleteListenRecodeMaterialRequest {
  /**
   * 资料 ID
   */
  materialId: string;
  /**
   * 会议 ID
   */
  listeningId: string;
}
/**
 * AddListeningParticipantReq
 */
export interface AddListeningParticipantRequest {
  /**
   * 目标会议 ID
   */
  listeningId?: string;
  /**
   * 需要添加的参会用户 ID 列表
   */
  userIds?: string[];
}
/**
 * GetSpeakerStatsRsp
 */
export interface GetListeningSpeakerStatisticsResponse {
  /**
   * 各发言人统计信息
   */
  speakerStats?: SpeakerStat[];
}

/**
 * SpeakerStat
 */
export interface SpeakerStat {
  /**
   * 发言时长 (毫秒)
   */
  duration?: string;
  /**
   * 发言人头像
   */
  speakerAvatar?: string;
  /**
   * 发言人标签
   */
  speakerLabel?: string;
  /**
   * 关联的用户 ID
   */
  speakerUserId?: string;
  /**
   * 发言占比 (0.0 - 1.0)
   */
  speakingPercentage?: number;
  isEditing?: boolean;
}
/**
 * GetListeningSummaryRsp
 */
export interface GetListeningSummaryResponse {
  /**
   * 章节列表
   */
  chapters?: ChapterInfo[];
  /**
   * 会议 ID
   */
  listeningId?: string;
  /**
   * 会议总结文本
   */
  summaryContent?: string;
  /**
   * 待办事项列表
   */
  todos?: TodoItem[];
}

/**
 * ChapterInfo
 */
export interface ChapterInfo {
  /**
   * 章节结束时间 (可选)
   */
  endOffset?: string;
  /**
   * 章节 ID
   */
  id: string;
  /**
   * 章节开始时间 (可选)
   */
  startOffset?: string;
  /**
   * 章节概要
   */
  summaryText?: string;
  /**
   * 章节标题
   */
  title?: string;
}

/**
 * TodoItem
 */
export interface TodoItem {
  /**
   * 待办事项描述
   */
  description?: string;
  /**
   * 截止日期 (时间戳, 0表示未设置)
   */
  dueDate?: number;
  /**
   * 待办事项 ID
   */
  id: string;
  /**
   * 关联的原文开始时间 (可选)
   */
  startOffset?: string;
}
/**
 * UpdateListeningSummaryContentReq
 */
export interface UpdateListeningSummaryRequest {
  /**
   * 会议 ID
   */
  listeningId?: string;
  /**
   * 更新后的总结文本
   */
  summaryContent?: string;
}
/**
 * AddListeningTodoReq
 */
export interface AddListeningTodoRequest {
  /**
   * 待办描述
   */
  description?: string;
  /**
   * 会议 ID
   */
  listeningId?: string;
}
/**
 * AddListeningTodoRsp
 */
export interface AddListeningTodoResponse {
  /**
   * 新增待办的 ID
   */
  todoId?: string;
}
export interface DeleteListeningTodoRequest {
  /**
   * 会议 ID
   */
  listeningId?: string;
  /**
   * 待办 ID
   */
  todoId?: string;
}
/**
 * UpdateListeningTodoReq
 */
export interface UpdateListeningTodoRequest {
  /**
   * 新描述
   */
  description?: string;
  /**
   * 会议 ID
   */
  listeningId?: string;
  /**
   * 待办 ID
   */
  todoId?: string;
}
export interface GetListeningTranscriptRequest {
  /**
   * 会议 ID
   */
  listeningId?: string;
  /**
   * 分页标记
   */
  pageToken?: string;
  /**
   * 每页片段数，例如最多500
   */
  perPage?: number;
}
/**
 * GetListeningTranscriptRsp
 */
export interface GetListeningTranscriptResponse {
  /**
   * 下一页 token
   */
  nextPageToken: string;
  /**
   * 转写片段列表
   */
  segments: TranscriptSegment[];
}

/**
 * TranscriptSegment
 */
export interface TranscriptSegment {
  /**
   * 片段结束时间 (毫秒)
   */
  endOffset?: string;
  /**
   * 片段 ID
   */
  id: string;
  /**
   * 发言人 ID
   */
  speakerId?: string;
  /**
   * 发言人角色（0：未知，1：老师，2：学生）
   */
  speakerRole?: number;
  /**
   * 发言人头像
   */
  speakerAvatar?: string;
  /**
   * 显示的发言人标签
   */
  speakerLabel?: string;
  /**
   * 发言人 id，可能为空
   */
  speakerUserId?: string;
 
  /**
   * 片段开始时间 (毫秒)
   */
  startOffset?: string;
  /**
   * 片段文本
   */
  transcriptText?: string;
  /**
   * 片段是否正在编辑
   */
  isEditing?: boolean;
  /**
   * 片段建议
   */
  suggestions?: {
    suggestionId: string;
    startIndex: number;
    endIndex: number;
    originalWord: string;
    suggestedWords: string[];
  }[];
}
/**
 * UpdateTranscriptSegmentReq
 */
export interface UpdateListenContentRequest {
  /**
   * 会议 ID
   */
  listeningId?: string;
  /**
   * 更新后的总结文本
   */
  summaryContent?: string;
}
/**
 * UpdateTranscriptSegmentReq
 */
export interface UpdateListenRecodeTranscriptRequest {
  /**
   * 会议 ID
   */
  listeningId?: string;
  /**
   * 片段 ID
   */
  segmentId?: string;
  /**
   * 更新后的文本内容
   */
  transcriptText?: string;
}
/**
 * ChangeListeningSpeakerReq，批量更改会议发言人的请求
 */
export interface UpdateListenRecodeTranscriptAllSpeakerRequest {
  /**
   * 会议 ID
   */
  listeningId?: string;
  /**
   * 通过发言人标签指定原始发言人
   */
  originalSpeaker?: {
    speakerId: string;
    speakerRole: number;
  };
  /**
   * 仅在源发言人是“未知”(speaker_role=0)时使用
   */
  originalSpeakerLabel?: string;
  /**
   * 修正后的发言人id
   */
  newSpeakerId: string;
  /**
   * 新发言人角色（0：未知，1：老师，2：学生）
   */
  newSpeakerRole: number;
}

export interface  UpdateListenRecodeTranscriptSpeakerRequest {  /**
  * 倾听 ID
  */
  listeningId: string 
 /**
   * 片段 ID
   */
  segmentId: string 
  /**
   * 新的发言人ID (可以是student_id或teacher_id) 
   */
  newSpeakerId: string;
  /**
   * 新的发言人角色 (1: 老师, 2: 学生)
   */
  newSpeakerRole: number
}

/**
 * CancelListeningReq
 */
export interface CancelListeningRequest {
  /**
   * 会议 ID
   */
  listeningId?: string;
}
/**
 * EndListeningRecordingReq
 */
export interface EndListeningRequest {
  /**
   * 会议 ID
   */
  listeningId?: string;
  /**
   * 离线模式下，传递录音文件的信息
   */
  // offlineRecording?: OfflineRecording
}

/**
 * 离线模式下，传递录音文件的信息
 *
 * ListeningRecordingInfo
 */
export interface OfflineRecording {
  /**
   * 录音时长（秒）
   */
  duration?: number;
  /**
   * 录音文件格式
   */
  fileFormat?: string;
  /**
   * 大小 (bytes)
   */
  fileSize?: number;
  /**
   * 录音文件 url
   */
  fileUrl?: string;
  id?: string;
}
/**
 * StartListeningRecordingReq
 */
export interface StartListeningRequest {
  /**
   * 会议 ID
   */
  listeningId?: string;
}
/**
 * StartListeningRecordingRsp
 */
export interface StartListeningResponse {
  /**
   * 客户端连接的 URL (例如 WebSocket URL)
   */
  meetingJoinUrl?: string;
  /**
   * 实时任务 ID
   */
  taskId?: string;
}
/**
 * TransferListeningOrganizerReq
 */
export interface TransferListeningRequest {
  /**
   * 会议 ID
   */
  listeningId?: string;
  /**
   * 新组织者的用户 ID
   */
  newOrganizerUserId?: string;
  /**
   * 转让人是否退出会议（0：否，1：是）
   */
  quit?: number;
}

// 获取列表
export const getListeningList = async (data: ListRequest) => {
  return await api.get<ListResponse>('/v1/affairs/listening', {
    params: data,
  });
};
//创建倾听记录
export const create = async (data: PostListenRequest) => {
  return await api.post('/v1/affairs/listening', data);
};

//获取基础信息
export const getListeningDetail = async (data: { listeningId: string }) => {
  const response = await api.get(`/v1/affairs/listening/${data.listeningId}`);
  return response;
};

//删除
export const deleteListening = async (data: { listeningId: string }) => {
  return await api.delete(`/v1/affairs/listening/${data.listeningId}`);
};

//导出音频,会议纪要,原文 1音频，2会议纪要，3原文
export const exportListeningRecode = async (
  data: ExportListenRecodeRequest
) => {
  let url = `/v1/affairs/listening/${data.listeningId}/exports/audio`;
  let params: Record<string, any> = {
    listeningId: data.listeningId,
  };
  if (data.type === 2) {
    url = `/v1/affairs/listening/${data.listeningId}/exports/summary`;
    params = {
      format: data.format,
    };
  }
  if (data.type === 3) {
    url = `/v1/affairs/listening/${data.listeningId}/exports/transcript`;
    params = {
      format: data.format,
      showSpeaker: data.showSpeaker,
      showTimestamp: data.showTimestamp,
    };
  }
  const response = await api.get(url, { params });
  return response;
};

//更新倾听内容
export const updateListeningContent = async (
  data: UpdateListenContentRequest
) => {
  return await api.patch(
    `/v1/affairs/listening/${data.listeningId}/summary/content`,
    data
  );
};

//更新倾听片段记录
export const updateListeningRecodeTranscriptSegments = async (
  data: UpdateListenRecodeTranscriptRequest
) => {
  return await api.patch(
    `/v1/affairs/listening/${data.listeningId}/transcript/segments/${data.segmentId}`,
    data
  );
};

//全局修正一个发言人的身份
export const updateListenRecodeTranscriptAllSpeaker = async (
  data: UpdateListenRecodeTranscriptAllSpeakerRequest
) => {
  return await api.post(
    `/v1/affairs/listening/${data.listeningId}/transcript:correctIdentity`,
    data
  );
};

// 更新倾听对话片段发言人信息
export const updateListenRecodeTranscriptSpeaker = async (
  data: UpdateListenRecodeTranscriptSpeakerRequest
) => {
  return await api.patch(
    `/v1/affairs/listening/${data.listeningId}/transcript/segments/${data.segmentId}/speaker`,
    data
  );
};
//取消会议（会议状态为"未开始"时可调用）
export const cancelListening = async (data: CancelListeningRequest) => {
  return await api.post(`/v1/affairs/listening/${data.listeningId}:cancel`);
};

export const endRecording = async (data: EndListeningRequest) => {
  return await api.post(
    `/v1/affairs/listening/${data.listeningId}:endRecording`,
    data
  );
};

export const startRecording = async (data: CancelListeningRequest) => {
  return await api.post(
    `/v1/affairs/listening/${data.listeningId}:startRecording`,
    data
  );
};

export const reGenerate = async (data: CancelListeningRequest) => {
  return await api.post(
    `/v1/affairs/listening/${data.listeningId}:regenerateSummary`,
    data
  );
};

export const updateMaterials = async (data: {
  listeningId: string;
  materials?: ListeningMaterial[];
}) => {
  return await api.patch(
    `/v1/affairs/listening/${data.listeningId}/materials`,
    data
  );
};


export const updateListeningSupportMeasure = async (
  data: UpdateListenContentRequest
) => {
  return await api.patch(
    `/v1/affairs/listening/${data.listeningId}/summary/support-measure`,
    {listeningId: data.listeningId, supportMeasure: data.summaryContent}
  );
};


export const updateListeningContextDescription = async (
  data: UpdateListenContentRequest
) => {
  // /v1/affairs/listening/{listeningId}/context-description
  return await api.patch(
    `/v1/affairs/listening/${data.listeningId}/context-description`,
    {listeningId: data.listeningId, contextDescription: data.summaryContent}
  );
};

// /v1/affairs/listening/{listeningId}/summary/interpretation
export const updateListeningInterpretation = async (
  data: UpdateListenContentRequest
) => {
  return await api.patch(
    `/v1/affairs/listening/${data.listeningId}/summary/interpretation`,
    {listeningId: data.listeningId, interpretationText: data.summaryContent}
  );
};

export const addTag = async (data: {
  listeningId: string;
  tagNames: string[];
}) => {
  return await api.post(
    `/v1/affairs/listening/${data.listeningId}/tags`,
    data
  );
};

export const deleteTag = async (data: {
  listeningId: string;
  tagIds: string[];
}) => {
  return await api.post(
    `/v1/affairs/listening/${data.listeningId}/tags:batchDelete`,
    data
  );
};

export const handleSuggestion = async (data: {
  listeningId: string;
  segmentId: string;
  suggestionId: string,
  action: 'accept' | 'ignore';
  acceptedWord?: string
}) => {
  return await api.patch(
    `/v1/affairs/listening/${data.listeningId}/segments/${data.segmentId}/suggestions/${data.suggestionId}`,
    data
  );
};
