/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
/** biome-ignore-all lint/nursery/noNoninteractiveElementInteractions: <explanation> */
'use client';
import { Toast } from 'antd-mobile';
import {
  ArrowLeft,
  Bookmark,
  Download,
  Heart,
  Keyboard,
  Loader2,
  Maximize2,
  MessageCircle,
  Music,
  Play,
  Send,
  Video,
} from 'lucide-react';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import {
  addComment,
  deleteComment,
  getCommentList,
  getLikeList,
  getSourceDetail,
  like,
  toggleBookmark,
} from '@/api/cloudDesk';
import { useCommonStore } from '@/store/useCommonStore';
import {
  getMobile,
  navigationToNativePage,
  onBackPressed,
  publishEventToNative,
} from '@/utils';
import { AudioPlayer } from './components/AudioPlayer';
import { CommentItem, formatDateTime } from './components/CommentItem';
import { DocumentPreview } from './components/DocumentPreview';
import { ImagePreview } from './components/ImagePreview';
import { VideoPlayer } from './components/VideoPlayer';

interface CommentFormData {
  placeholder: string;
  content: string;
}

export default function ResourceDetail() {
  const searchParams = useSearchParams();
  const id = searchParams.get('id');
  const device = getMobile();

  // 将 getFileType 函数移动到这里
  const getFileType = (info: any) => {
    const fileType = (info.file?.ext || info.name.split('.').pop())?.toLowerCase();
    if (
      [
        'mp4', 'mpeg', 'mpg', 'avi', 'mov', 'wmv', 'mkv', '3gp', 'flv',
        'webm', 'ogv', 'ts', 'm4v', 'asf', 'rm', 'rmvb', 'vob', 'f4v',
        'swf', 'mts', 'm2ts', 'divx', 'xvid', 'qt', 'mxf'
      ].includes(fileType)
    ) {
      return 'video';
    }
    if (
      [
        'mp3', 'aac', 'wav', 'm4a', 'flac', 'ogg', 'aiff', 'wma', 'mid',
        'midi', 'amr', 'ape', 'ac3', 'dts', 'opus', 'ra', 'au', 'snd',
        'aifc', 'caf', 'mp2', 'mpa', 'mpc', 'tta', 'wv', 'dsf', 'dff'
      ].includes(fileType)
    ) {
      return 'audio';
    }
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(fileType)) {
      return 'image';
    }
    if (
      [
        'docx',
        'dotx',
        'dot',
        'doc',
        'xls',
        'xlsx',
        'xlsm',
        'ppt',
        'pptx',
      ].includes(fileType)
    ) {
      return 'document';
    }
    if (['pdf'].includes(fileType)) {
      return 'pdf';
    }
    if (['csv', 'txt'].includes(fileType)) {
      return 'txt';
    }
    if (['zip', 'rar'].includes(fileType)) {
      return 'zip';
    }
    return 'other';
  };

  // 然后是所有的 state 声明
  const [activeTab, setActiveTab] = useState<'comments' | 'likes'>('comments');
  const [commentForm, setCommentForm] = useState<CommentFormData>({
    placeholder: '请输入评论内容',
    content: '',
  });
  const [isLiking, setIsLiking] = useState(false);
  const [showCommentInput, setShowCommentInput] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [isFail, setIsFail] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [resource, setResource] = useState<any>(null);
  // 使用动态获取的点赞用户列表
  const [likedUsers, setLikedUsers] = useState<any[]>([]);
  const [commentList, setCommentList] = useState<any[]>([]);
  // 在现有状态变量后添加iframe loading状态
  const [showPreview, setShowPreview] = useState<boolean>(false);
  const [iframeLoading, setIframeLoading] = useState<boolean>(true);
  const { userId } = useCommonStore.getState();
  const { teacherName } = useCommonStore.getState();

  const processedComments = (list: any[]) => {
    const result = list.map((comment: any) => {
      const parentCommentDelete =
        comment.parentId && comment.parentId !== '0'
          ? !list.some((c: any) => c.commentId === comment.parentId)
          : false;

      return {
        ...comment,
        parentCommentDelete,
      };
    });
    return result;
  };

  useEffect(() => {
    if (!id) {
      setIsFail(true);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    Promise.all([getSourceDetail(id), getLikeList(id), getCommentList(id)])
      .then(([sourceDetail, likeList, commentsRes]: [any, any, any]) => {
        const isLiked = (likeList as [any]).some(
          (user: any) => user.likeUser.id === userId
        );
        console.log('userid', userId);
        console.log('likeList', likeList);
        console.log('isLiked', isLiked);
        const type = getFileType(sourceDetail);
        console.log('type', type);
        setCommentList(processedComments(commentsRes.list));
        setLikedUsers(likeList);
        setResource({
          ...sourceDetail,
          isLiked,
          type,
        });
      })
      .catch((e) => {
        console.log('e', e);
        setIsFail(true);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [id, userId]);

  useEffect(() => {
    if (resource) {
      document.title = resource.name;
    }
  }, [resource]);
  // 构建微软Office Online Viewer的URL
  const getIframeUrl = (r: any) => {
      if (r.type === 'zip' || r.type === 'txt' || r.type === 'pdf') {
        return r.signUrl;
      }
      if (r.type === 'pdf') {
        return `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(r.signUrl)}&toolbar=0`;
      }
      // 微软Office Online Viewer - 设置宽度为页面宽度
      return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(r.signUrl)}&wdAr=0.46&wdEmbedCode=0`;
  };
  if (isLoading) {
    // Loading状态
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-orange-50 to-green-50">
        <div className="fixed top-0 right-0 left-0 z-20 bg-white px-4">
          <div className="flex items-center gap-3">
            {/* 返回按钮 */}
            <button
              className="flex-shrink-0 rounded-lg p-2"
              onClick={() => {
                onBackPressed();
              }}
              type="button"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
          </div>
        </div>
        <div className="text-center">
          <Loader2 className="mx-auto mb-4 h-8 w-8 animate-spin text-orange-500" />
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (isFail) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-orange-50 to-green-50">
        <div className="fixed top-0 right-0 left-0 z-20 bg-white px-4">
          <div className="flex items-center">
            {/* 返回按钮 */}
            <button
              className="flex-shrink-0 rounded-lg py-2 transition-colors hover:bg-gray-100"
              onClick={() => {
                onBackPressed();
              }}
              type="button"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
          </div>
        </div>
        <div className="text-center">
          <h2 className="mb-4 font-bold text-2xl text-gray-800">资源未找到</h2>
          <button
            className="rounded-lg bg-orange-500 px-6 py-2 text-white transition-colors hover:bg-orange-600"
            onClick={() => {
              onBackPressed();
            }}
            type="button"
          >
            返回列表
          </button>
        </div>
      </div>
    );
  }
  const handleBookmark = () => {
    toggleBookmark({
      id: resource.sourceId,
      isBookmark: !resource.starred,
    }).then(() => {
      Toast.show({
        content: resource.starred ? '已取消收藏' : '已收藏',
        icon: 'success',
      });
      setResource({
        ...resource,
        starred: !resource.starred,
      });
      publishEventToNative(
        'activity_on_resume',
        JSON.stringify({
          sourceId: resource.sourceId,
          starred: !resource.starred,
        })
      );
    });
  };
  const handleLike = () => {
    if (isLiking) {
      return;
    }

    setIsLiking(true);
    like({ id: resource.sourceId })
      .then(() => {
        setResource({
          ...resource,
          isLiked: !resource.isLiked,
        });
        getLikeList(resource.sourceId).then((res: any) => {
          setLikedUsers(res);
        });
        Toast.show({
          content: resource.isLiked ? '已取消点赞' : '已点赞',
          icon: 'success',
        });
      })
      .finally(() => {
        setIsLiking(false);
      });
  };

  const handleDownload = () => {
    navigationToNativePage(
      `app://app/cloud/fileOpened?sourceId=${resource.sourceId}&fileName=${encodeURIComponent(resource.file.originName)}&fileSize=${resource.file.size}&ext=${encodeURIComponent(resource.file.ext)}&fileId=${encodeURIComponent(resource.file.fileId)}&signUrl=${encodeURIComponent(resource.signUrl)}`
    );
  };
  const handleSubmitComment = () => {
    if (!commentForm.content.trim()) {
      Toast.show({ content: '请输入评论内容' });
      return;
    }

    if (replyingTo) {
      addComment({
        sourceId: resource.sourceId,
        content: commentForm.content,
        parentId: replyingTo,
      }).then(() => {
        Toast.show({ content: '回复发布成功', icon: 'success' });
        getCommentList(resource.sourceId).then((res: any) => {
          setCommentList(processedComments(res.list));
        });
      });
    } else {
      addComment({
        sourceId: resource.sourceId,
        content: commentForm.content,
        parentId: '0',
      }).then(() => {
        Toast.show({ content: '评论发布成功', icon: 'success' });
        getCommentList(resource.sourceId).then((res: any) => {
          setCommentList(processedComments(res.list));
        });
      });
    }

    setCommentForm({ placeholder: '请输入评论内容', content: '' });
    setShowCommentInput(false);
    setReplyingTo(null);
  };

  const handleShowCommentInput = () => {
    setActiveTab('comments');
    setShowCommentInput(true);
    setReplyingTo(null);
  };

  const handleReply = (commentId: string, userName: string) => {
    setActiveTab('comments');
    setReplyingTo(commentId);
    setCommentForm({ placeholder: `回复${userName}`, content: '' });
    setShowCommentInput(true);
  };

  const handlePreview = () => {
    if (resource.type === 'pdf' && device === 'android') {
      handleDownload()
     return;
    }
    setShowPreview(true);
  };

  const handleClosePreview = () => {
    setShowPreview(false);
  };
  // 在现有的handler函数后添加删除评论的处理函数
  const handleDeleteComment = async (commentId: string) => {
    try {
      await deleteComment(commentId);
      // 重新获取评论列表
      const updatedComments: any = await getCommentList(resource.sourceId);
      setCommentList(processedComments(updatedComments.list));
      Toast.show({ content: '评论删除成功' });
    } catch (error) {
      console.error('删除评论失败:', error);
      Toast.show({ content: '删除评论失败，请重试' });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-green-50 pb-20">
      <div className="fixed top-0 right-0 left-0 z-20 bg-white px-4">
        <div className="flex items-center gap-3">
          {/* 返回按钮 */}
          <button
            className="flex-shrink-0 rounded-lg p-2"
            onClick={() => {
              onBackPressed();
            }}
            type="button"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          {/* 搜索框 - 居中 */}
          <div
            className="relative flex-1 cursor-pointer pr-12 text-center"
            style={{
              display: '-webkit-box',
              WebkitBoxOrient: 'vertical',
              WebkitLineClamp: 2,
              overflow: 'hidden',
              lineHeight: '1.2em',
              maxHeight: '2.4em',
            }}
          >
            <span
              style={{
                display: 'block',
                textOverflow: 'ellipsis',
                whiteSpace: 'break-spaces',
                wordBreak: 'break-word',
              }}
            >
              {resource.name}
            </span>
          </div>
        </div>
      </div>
      {/* 资源详细信息 */}
      {resource.owner && (
        <div className="mb-4 flex items-start gap-3 px-4 pt-14">
          <picture>
            <img
              alt={resource.owner.id}
              className="h-12 w-12 flex-shrink-0 rounded-full object-cover"
              src={resource.owner.avatar}
            />
          </picture>
          <div className="min-w-0 flex-1">
            {/* 用户信息和评分 */}
            <div className="flex flex-wrap items-center gap-2">
              <span className="font-medium text-gray-800">
                {resource.owner.name}
              </span>
              <span className="text-gray-500 text-sm">
                {formatDateTime(resource.createTime)}
              </span>
            </div>

            {/* 幼儿园信息 */}
            {resource.owner.institution.name && (
              <div>
                <span className="text-gray-500 text-xs">
                  {resource.owner.institution.name}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      <div
        className="px-4 py-4"
        style={{ paddingTop: resource.owner ? '0' : '50px' }}
      >
        {/* 资源主要信息 */}
        {(resource.type !== 'other' && resource.type !== 'zip' && !(resource.type === 'pdf' && device === 'android')) && (
          <div className="mb-6 overflow-hidden rounded-xl bg-white shadow-lg">
            {/* 资源预览图 */}
            <div className="relative h-48 bg-gradient-to-r from-orange-100 to-green-100">
              {(resource.type === 'document' ||
                resource.type === 'txt' ||
                resource.type === 'pdf' ||
                resource.type === 'zip') && (
                <div className="relative h-full w-full" onClick={handlePreview}>
                  {iframeLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
                      <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                    </div>
                  )}
                  <iframe
                    className="h-full w-full"
                    onError={() => setIframeLoading(false)}
                    onLoad={() => setIframeLoading(false)}
                    src={getIframeUrl(resource)}
                    sandbox="allow-scripts allow-same-origin allow-popups allow-forms allow-downloads"
                    title={resource.name}
                  />
                </div>
              )}
              {resource.type === 'video' && resource.file.thumb && (
                <div className="h-full w-full">
                  <picture>
                    <img
                      alt={resource.sourceId}
                      className="h-full w-full cursor-pointer object-cover"
                      onClick={handlePreview}
                      src={resource.file.thumb}
                    />
                  </picture>
                </div>
              )}
              {resource.type === 'video' && !resource.file.thumb && (
                <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2">
                  <Video className="h-12 w-12 text-gray-600" />
                </div>
              )}
              {resource.type === 'audio' && (
                <div className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-1/2">
                  <Music className="h-12 w-12 text-gray-600" />
                </div>
              )}
              {resource.type === 'image' && (
                <div className="h-full w-full">
                  <picture>
                    <img
                      alt={resource.sourceId}
                      className="h-full w-full cursor-pointer object-cover"
                      onClick={handlePreview}
                      src={resource.signUrl}
                    />
                  </picture>
                </div>
              )}
              {(resource.type === 'video' || resource.type === 'audio') && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <button
                    className="flex h-16 w-16 transform items-center justify-center rounded-full bg-black/60 transition-all duration-200 hover:scale-110 hover:bg-black/80 active:scale-95"
                    onClick={handlePreview}
                    type="button"
                  >
                    <Play
                      className="ml-1 h-8 w-8 text-white"
                      fill="currentColor"
                    />
                  </button>
                </div>
              )}

              {/* 右下角额外的类型指示按钮 */}
              <div className="absolute right-3 bottom-3">
                {(resource.type === 'document' ||
                  resource.type === 'pdf' ||
                  resource.type === 'txt' ||
                  resource.type === 'zip') && (
                  <div
                    className="flex items-center gap-1 rounded-full bg-gray-500/90 px-2 py-1 font-medium text-white text-xs backdrop-blur-sm"
                    onClick={handlePreview}
                  >
                    <Maximize2 className="h-3 w-3" />
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Tab导航和内容区域 */}
        <div className="overflow-hidden rounded-xl bg-white shadow-lg">
          {/* Tab导航 */}
          <div className="flex border-gray-200 border-b">
            <button
              className={`flex-1 px-4 py-3 text-center font-medium transition-colors ${
                activeTab === 'comments'
                  ? 'border-orange-600 border-b-2 bg-orange-50 text-orange-600'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('comments')}
              type="button"
            >
              <div className="flex items-center justify-center gap-2">
                <MessageCircle className="h-4 w-4" />
                <span>评论 ({commentList.length})</span>
              </div>
            </button>
            <button
              className={`flex-1 px-4 py-3 text-center font-medium transition-colors ${
                activeTab === 'likes'
                  ? 'border-orange-600 border-b-2 bg-orange-50 text-orange-600'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('likes')}
              type="button"
            >
              <div className="flex items-center justify-center gap-2">
                <Heart className="h-4 w-4" />
                <span>点赞 ({likedUsers.length})</span>
              </div>
            </button>
          </div>

          {/* Tab内容 */}
          <div className="p-4">
            {activeTab === 'comments' ? (
              <div>
                {/* 评论列表 */}
                <div className="space-y-4">
                  {commentList.length === 0 ? (
                    <div className="py-8 text-center text-gray-500">
                      <MessageCircle className="mx-auto mb-3 h-12 w-12 text-gray-300" />
                      <p>暂无评论，快来发表第一条评论吧！</p>
                      <button
                        className="mt-4 rounded-lg bg-orange-500 px-6 py-2 text-white transition-colors hover:bg-orange-600"
                        onClick={handleShowCommentInput}
                        type="button"
                      >
                        写评论
                      </button>
                    </div>
                  ) : (
                    commentList.map((comment: any) => (
                      <CommentItem
                        comment={comment}
                        currentUser={{
                          id: userId,
                          name: teacherName,
                        }}
                        key={comment.commentId}
                        onDelete={handleDeleteComment}
                        onReply={(commentId, userName) =>
                          handleReply(commentId, userName)
                        }
                      />
                    ))
                  )}
                </div>
              </div>
            ) : (
              <div>
                {/* 点赞列表 */}
                <div className="space-y-3">
                  {likedUsers.length === 0 ? (
                    <div className="py-8 text-center text-gray-500">
                      <Heart className="mx-auto mb-3 h-12 w-12 text-gray-300" />
                      <p>暂无点赞，快来点个赞吧！</p>
                    </div>
                  ) : (
                    likedUsers.map((user: any) => (
                      <div
                        className="flex items-center gap-3 rounded-lg bg-gray-50 p-3"
                        key={user.id}
                      >
                        <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-300">
                          <picture>
                            <img
                              alt={user.likeUser.id}
                              className="h-10 w-10 flex-shrink-0 rounded-full object-cover"
                              src={user.likeUser.avatar}
                            />
                          </picture>
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-gray-800">
                            {user.likeUser.name}
                          </div>
                          <div className="text-gray-500 text-sm">
                            {user.likeInst.name}
                          </div>
                        </div>
                        <div className="text-gray-400 text-xs">
                          {formatDateTime(user.updateTime || user.createTime)}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 底部操作栏 */}
      <div className="safe-area-pb fixed right-0 bottom-0 left-0 border-gray-200 border-t bg-white px-4 py-3">
        <div className="mx-auto flex max-w-md items-center justify-between">
          {/* 点赞按钮 */}
          <button
            className={`flex min-w-[60px] flex-col items-center justify-center gap-1 rounded-lg px-3 py-2 ${
              resource.isLiked
                ? 'text-red-600'
                : 'text-gray-600 active:scale-95'
            } ${isLiking ? 'pulse-no-offset' : ''}`}
            disabled={isLiking}
            onClick={handleLike}
            type="button"
          >
            <div className="relative">
              <Heart
                className={`h-6 w-6 transition-all duration-300 ${
                  resource.isLiked ? 'fill-current text-red-500' : ''
                } ${isLiking ? 'animate-bounce' : ''}`}
              />
              {/* 点赞时的心形爆炸动画 */}
              {isLiking && (
                <>
                  {/* 主要的心形放大动画 */}
                  <Heart className="absolute inset-0 h-6 w-6 animate-ping fill-current text-red-500" />
                  {/* 小心形粒子效果 */}
                  <div className="-top-1 -left-1 absolute h-2 w-2">
                    <Heart className="h-full w-full animate-bounce fill-current text-pink-400" />
                  </div>
                  <div className="-top-1 -right-1 absolute h-2 w-2">
                    <Heart className="h-full w-full animate-bounce fill-current text-pink-400 delay-75" />
                  </div>
                  <div className="-bottom-1 -left-1 absolute h-2 w-2">
                    <Heart className="h-full w-full animate-bounce fill-current text-pink-400 delay-150" />
                  </div>
                  <div className="-bottom-1 -right-1 absolute h-2 w-2">
                    <Heart className="h-full w-full animate-bounce fill-current text-pink-400 delay-200" />
                  </div>
                </>
              )}
            </div>
            <span className="text-xs">{likedUsers.length || 0}</span>
          </button>

          {/* 评论按钮 */}
          <button
            className="flex min-w-[60px] touch-manipulation flex-col items-center justify-center gap-1 rounded-lg px-3 py-2 text-gray-600 transition-all duration-200 active:scale-95"
            onClick={handleShowCommentInput}
            type="button"
          >
            <Keyboard className="h-6 w-6" />
            <span className="text-xs">评论</span>
          </button>

          {/* 收藏按钮 */}
          <button
            className={`flex min-w-[60px] touch-manipulation flex-col items-center justify-center gap-1 rounded-lg px-3 py-2 transition-all duration-200 ${
              resource.starred
                ? 'text-orange-600'
                : 'text-gray-600 active:scale-95'
            }`}
            onClick={handleBookmark}
            type="button"
          >
            <Bookmark
              className={`h-6 w-6 transition-all duration-200 ${
                resource.starred ? 'fill-current' : ''
              }`}
            />
            <span className="text-xs">收藏</span>
          </button>

          {/* 下载按钮 */}
          <button
            className="flex min-w-[60px] touch-manipulation flex-col items-center justify-center gap-1 rounded-lg px-3 py-2 text-gray-600 transition-all duration-200 active:scale-95"
            onClick={handleDownload}
            type="button"
          >
            <Download className="h-6 w-6" />
            <span className="text-xs">下载</span>
          </button>
        </div>
      </div>

      {/* 评论输入框 - 键盘上方弹出 */}
      {showCommentInput && (
        <div
          className="fixed inset-0 z-50 bg-black/50"
          onClick={() => setShowCommentInput(false)}
        >
          <div
            className="safe-area-pb fixed right-0 bottom-0 left-0 border-gray-200 border-t bg-white p-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center gap-3">
              <div className="flex-1">
                <textarea
                  autoFocus
                  className="w-full resize-none rounded-lg border border-gray-300 p-3 text-base focus:border-transparent focus:ring-2 focus:ring-orange-500"
                  onChange={(e) =>
                    setCommentForm((prev) => ({
                      ...prev,
                      content: e.target.value,
                    }))
                  }
                  placeholder={commentForm.placeholder}
                  rows={3}
                  value={commentForm.content}
                />
              </div>
              <button
                className={`touch-manipulation rounded-lg p-3 transition-colors ${
                  commentForm.content.trim()
                    ? 'bg-orange-500 text-white hover:bg-orange-600'
                    : 'cursor-not-allowed bg-gray-200 text-gray-400'
                }`}
                disabled={!commentForm.content.trim()}
                onClick={handleSubmitComment}
                type="button"
              >
                <Send className="h-5 w-5" />
              </button>
            </div>
            {replyingTo && (
              <div className="mt-2 text-gray-500 text-sm">
                正在回复评论
                <button
                  className="ml-2 text-orange-500 hover:text-orange-600"
                  onClick={() => {
                    setReplyingTo(null);
                    setCommentForm({
                      placeholder: '请输入评论内容',
                      content: '',
                    });
                  }}
                  type="button"
                >
                  取消回复
                </button>
              </div>
            )}
          </div>
        </div>
      )}
      {/* 预览组件 */}
      {showPreview && (
        <>
          {resource.type === 'image' && (
            <ImagePreview
              alt={resource.name}
              onClose={handleClosePreview}
              src={resource.signUrl}
            />
          )}
          {resource.type === 'video' && (
            <VideoPlayer
              onClose={handleClosePreview}
              src={resource.signUrl}
              title={resource.name}
            />
          )}
          {resource.type === 'audio' && (
            <AudioPlayer
              onClose={handleClosePreview}
              src={resource.signUrl}
              title={resource.name}
            />
          )}

          {(resource.type === 'document' ||
            resource.type === 'txt' ||
            resource.type === 'pdf' ||
            resource.type === 'zip') && (
            <DocumentPreview
              onClose={handleClosePreview}
              src={resource.signUrl}
              title={resource.name}
              type={resource.type}
            />
          )}
        </>
      )}
    </div>
  );
}
