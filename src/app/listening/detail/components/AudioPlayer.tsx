"use client";

import { Toast } from "antd-mobile";
import { Pause, Play, SkipBack, SkipForward } from "lucide-react";
import { useEffect, useRef, useState } from "react";

interface AudioPlayerProps {
	audioUrl: string;
	positionSeconds?: number; // 新增：外部控制的播放位置
	onPlaybackStatusUpdate?: (status: {
		isPlaying: boolean;
		positionMillis: number;
		durationMillis: number;
	}) => void;
	onSeek?: (position: number) => void;
}

export default function AudioPlayer({
	audioUrl,
	positionSeconds, // 新增参数
	onPlaybackStatusUpdate,
	onSeek,
}: AudioPlayerProps) {
	const audioRef = useRef<HTMLAudioElement>(null);
	const [isPlaying, setIsPlaying] = useState(false);
	const [currentTime, setCurrentTime] = useState(0);
	const [duration, setDuration] = useState(0);
	const [isLoading, setIsLoading] = useState(false);
	const [isKbOpen, setIsKbOpen] = useState(false); // ★ 新增：键盘开关状态

	// 格式化时间显示
	const formatTime = (seconds: number) => {
		const mins = Math.floor(seconds / 60);
		const secs = Math.floor(seconds % 60);
		return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
	};

	// 播放/暂停
	const togglePlayback = async () => {
		if (!audioRef.current) return;

		try {
			if (isPlaying) {
				audioRef.current.pause();
				setIsPlaying(false);
			} else {
				setIsLoading(true);
				await audioRef.current.play();
				setIsPlaying(true);
			}
		} catch (error) {
			console.error("播放失败:", error);
			Toast.show("播放失败，请重试");
		} finally {
			setIsLoading(false);
		}
	};

	// 快进
	const skipForward = () => {
		if (!audioRef.current) return;
		const newTime = Math.min(audioRef.current.currentTime + 15, duration);
		audioRef.current.currentTime = newTime;
		setCurrentTime(newTime);
		onSeek?.(newTime * 1000);
	};

	// 快退
	const skipBackward = () => {
		if (!audioRef.current) return;
		const newTime = Math.max(audioRef.current.currentTime - 15, 0);
		audioRef.current.currentTime = newTime;
		setCurrentTime(newTime);
		onSeek?.(newTime * 1000);
	};

	// 拖拽进度条
	const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
		if (!audioRef.current) return;
		const newTime = Number.parseFloat(e.target.value);
		audioRef.current.currentTime = newTime;
		setCurrentTime(newTime);
		onSeek?.(newTime * 1000);
	};

	// 音频事件监听
	useEffect(() => {
		const audio = audioRef.current;
		if (!audio) return;

		const handleLoadedMetadata = () => {
			setDuration(audio.duration);
		};

		const handleTimeUpdate = () => {
			setCurrentTime(audio.currentTime);
			onPlaybackStatusUpdate?.({
				isPlaying,
				positionMillis: audio.currentTime * 1000,
				durationMillis: audio.duration * 1000,
			});
		};

		const handleEnded = () => setIsPlaying(false);
		const handleError = () => {
			Toast.show("音频加载失败");
			setIsLoading(false);
		};

		audio.addEventListener("loadedmetadata", handleLoadedMetadata);
		audio.addEventListener("timeupdate", handleTimeUpdate);
		audio.addEventListener("ended", handleEnded);
		audio.addEventListener("error", handleError);

		return () => {
			audio.removeEventListener("loadedmetadata", handleLoadedMetadata);
			audio.removeEventListener("timeupdate", handleTimeUpdate);
			audio.removeEventListener("ended", handleEnded);
			audio.removeEventListener("error", handleError);
		};
	}, [audioUrl]); // 只依赖audioUrl，避免重复绑定事件

	// 单独处理播放状态更新
	useEffect(() => {
		if (onPlaybackStatusUpdate && audioRef.current) {
			onPlaybackStatusUpdate({
				isPlaying,
				positionMillis: audioRef.current.currentTime * 1000,
				durationMillis: audioRef.current.duration * 1000,
			});
		}
	}, [isPlaying, onPlaybackStatusUpdate]);

	// 新增：监听外部positionSeconds变化并跳转音频
	useEffect(() => {
		if (
			positionSeconds !== undefined &&
			audioRef.current &&
			!Number.isNaN(positionSeconds)
		) {
			const targetTime = positionSeconds;
			if (Math.abs(audioRef.current.currentTime - targetTime) > 0.5) {
				// 避免频繁跳转
				audioRef.current.currentTime = targetTime;
				setCurrentTime(targetTime);
			}
		}
	}, [positionSeconds]);

	// ===== 替换：测量播放器高度并写入 CSS 变量（键盘开启时不占位） =====
	const containerRef = useRef<HTMLDivElement>(null);
	useEffect(() => {
		const writeHeightVar = () => {
			const root = document.documentElement;
			if (!containerRef.current) return;
			if (isKbOpen) {
				root.style.setProperty("--audio-player-height", "0px");
				return;
			}
			const h = containerRef.current.offsetHeight;
			root.style.setProperty("--audio-player-height", `${h}px`);
		};

		writeHeightVar();

		const ro = new ResizeObserver(writeHeightVar);
		if (containerRef.current) ro.observe(containerRef.current);

		const onResize = () => writeHeightVar();
		window.addEventListener("resize", onResize);

		return () => {
			window.removeEventListener("resize", onResize);
			ro.disconnect();
		};
	}, [isKbOpen]);

	// ===== 新增：监听键盘开关，避免与页面 effect 抢写高度变量 =====
	useEffect(() => {
		if (typeof window === "undefined") return;
		const root = document.documentElement;

		const update = () => {
			const vv = window.visualViewport;
			const open = !!vv && window.innerHeight - vv.height > 120; // 与 page.tsx 阈值一致
			setIsKbOpen(open);
			if (open) {
				root.style.setProperty("--audio-player-height", "0px");
			} else if (containerRef.current) {
				root.style.setProperty(
					"--audio-player-height",
					`${containerRef.current.offsetHeight}px`,
				);
			}
		};

		update();
		window.visualViewport?.addEventListener("resize", update);
		window.visualViewport?.addEventListener("scroll", update);

		const onFocusIn = () => update();
		const onFocusOut = () => setTimeout(update, 60);
		document.addEventListener("focusin", onFocusIn);
		document.addEventListener("focusout", onFocusOut);

		return () => {
			window.visualViewport?.removeEventListener("resize", update);
			window.visualViewport?.removeEventListener("scroll", update);
			document.removeEventListener("focusin", onFocusIn);
			document.removeEventListener("focusout", onFocusOut);
		};
	}, []);

	return (
		<div
			ref={containerRef}
			className="fixed right-0 bottom-0 left-0 z-[60] border-gray-100 border-t bg-white shadow-[0_-4px_20px_rgba(0,0,0,0.1)] pb-[env(safe-area-inset-bottom)] transition-transform duration-150"
			style={{ transform: isKbOpen ? "translateY(100%)" : "translateY(0)" }}
			aria-hidden={isKbOpen}
		>
			<div className="px-6 py-4">
				<audio preload="metadata" ref={audioRef} src={audioUrl}>
					<track kind="captions" src={audioUrl} />
				</audio>

				{/* 时间显示 */}
				<div className="mb-3 flex items-center justify-between">
					<span className="font-medium text-gray-600 text-sm">
						{formatTime(currentTime)}
					</span>
					<span className="font-medium text-gray-600 text-sm">
						{formatTime(duration)}
					</span>
				</div>

				{/* 进度条 */}
				<div className="mb-4">
					<input
						className="slider h-3 w-full cursor-pointer appearance-none rounded-full bg-transparent"
						disabled={!duration || isLoading}
						max={duration || 0}
						min={0}
						onChange={handleSeek}
						style={{
							background: `linear-gradient(to right, var(--color-primary-500) 0%, var(--color-primary-500) ${(currentTime / duration) * 100 || 0}%, #e5e7eb ${(currentTime / duration) * 100 || 0}%, #e5e7eb 100%)`,
						}}
						type="range"
						value={currentTime}
					/>
				</div>

				<style jsx>{`
          .slider::-webkit-slider-thumb {
            appearance: none;
            height: 16px;
            width: 16px;
            border-radius: 50%;
            background: var(--color-primary-500);
            cursor: pointer;
            border: 3px solid #ffffff;
            box-shadow: 0 2px 8px rgba(14, 165, 233, 0.3);
            transition: all 0.2s ease;
          }

          .slider::-webkit-slider-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.4);
          }

          .slider::-webkit-slider-track {
            background: transparent;
            border-radius: 999px;
            height: 8px;
          }

          /* Firefox样式 */
          .slider::-moz-range-thumb {
            appearance: none;
            height: 16px;
            width: 16px;
            border-radius: 50%;
            background: var(--color-primary-500);
            cursor: pointer;
            border: 3px solid #ffffff;
            box-shadow: 0 2px 8px rgba(14, 165, 233, 0.3);
            transition: all 0.2s ease;
          }

          .slider::-moz-range-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.4);
          }

          .slider::-moz-range-track {
            background: transparent;
            border-radius: 999px;
            height: 8px;
          }

          .slider::-moz-range-progress {
            background: var(--color-primary-500);
            border-radius: 999px;
            height: 8px;
          }
        `}</style>

				{/* 播放控制按钮 */}
				<div className="flex items-center justify-center space-x-12">
					<button
						className={`relative rounded-full bg-gray-100 p-3 transition-all duration-200 hover:scale-105 hover:bg-gray-200 ${isLoading ? "cursor-not-allowed opacity-50" : ""}`}
						disabled={isLoading}
						onClick={skipBackward}
						type="button"
					>
						<SkipBack className="h-6 w-6 text-gray-700" />
					</button>

					<button
						className={`rounded-full p-4 text-white shadow-lg transition-all duration-200 hover:scale-105 ${isLoading ? "cursor-not-allowed opacity-75" : ""}`}
						disabled={isLoading}
						onClick={togglePlayback}
						style={{ backgroundColor: "var(--color-primary-500)" }}
						type="button"
					>
						{(() => {
							if (isLoading) {
								return (
									<div className="h-7 w-7 animate-spin rounded-full border-2 border-white border-t-transparent" />
								);
							}
							if (isPlaying) return <Pause className="h-7 w-7" />;
							return <Play className="ml-0.5 h-7 w-7" />;
						})()}
					</button>

					<button
						className={`relative rounded-full bg-gray-100 p-3 transition-all duration-200 hover:scale-105 hover:bg-gray-200 ${isLoading ? "cursor-not-allowed opacity-50" : ""}`}
						disabled={isLoading}
						onClick={skipForward}
						type="button"
					>
						<SkipForward className="h-6 w-6 text-gray-700" />
					</button>
				</div>
			</div>
		</div>
	);
}
