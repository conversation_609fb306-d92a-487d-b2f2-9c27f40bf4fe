"use client";

import { EditIcon } from "lucide-react";

type ContextDescriptionSectionProps = {
	contextDescription?: string;
	canEdit: boolean;
	onEdit: () => void;
};

export default function ContextDescriptionSection({
	contextDescription,
	canEdit,
	onEdit,
}: ContextDescriptionSectionProps) {
	return (
		<div className="mt-2 rounded-lg bg-white p-4 shadow-sm">
			<div className="mb-4 flex items-center justify-between">
				<div className="flex items-center">
					<div className="mr-2 h-4 w-1 rounded bg-blue-500" />
					<h2 className="font-semibold text-gray-900 text-lg">情景说明</h2>
				</div>
				{canEdit && (
					<button
						className="text-gray-400 hover:text-gray-600"
						type="button"
						onClick={onEdit}
					>
						<EditIcon className="h-5 w-5" />
					</button>
				)}
			</div>

			<p className="text-gray-800 text-sm leading-relaxed whitespace-pre-wrap">
				{contextDescription || "暂无情景说明"}
			</p>
		</div>
	);
}
