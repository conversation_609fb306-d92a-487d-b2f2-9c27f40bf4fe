/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
"use client";

import { ActionSheet, Radio, Toast } from "antd-mobile";
import { useState } from "react";

interface ExportFileProps {
	visible: boolean;
	onClose: () => void;
	onExport: (options: ExportOptions) => void;
	listeningId: string;
}

export interface ExportOptions {
	format: "pdf" | "word" | "audio";
	fileFormat?: "docx" | "pdf"; // 文档格式选择
	includeTranscript: boolean;
	includeSummary: boolean;
	includeParticipants: boolean;
}

export default function ExportFile({
	visible,
	onClose,
	onExport,
}: ExportFileProps) {
	const [exportOptions, setExportOptions] = useState<ExportOptions>({
		format: "word", // 默认选择倾听内容
		fileFormat: "docx", // 默认选择.docx格式
		includeTranscript: true,
		includeSummary: true,
		includeParticipants: true,
	});

	const handleExport = () => {
		onExport(exportOptions);
		onClose();
		Toast.show("导出任务已开始，请稍候...");
	};

	const contentOptions = [
		{ label: "音频", value: "audio" },
		{
			label: "倾听内容",
			value: "word",
		},
	];

	const fileFormatOptions = [
		{ label: ".docx", value: "docx" },
		{ label: ".pdf", value: "pdf" },
	];

	const actions = [
		{
			key: "export",
			text: (
				<div className="space-y-6 p-4">
					{/* 标题 */}
					<div className="flex items-center justify-between">
						<h3 className="font-medium text-lg">导出倾听内容</h3>
						<button
							className="text-gray-400 hover:text-gray-600"
							onClick={onClose}
							type="button"
						>
							✕
						</button>
					</div>

					{/* 导出内容选择 */}
					<div className="space-y-3">
						<h4 className="text-left font-medium text-gray-700">导出内容</h4>
						<Radio.Group
							onChange={(value) =>
								setExportOptions({
									...exportOptions,
									format: value as any,
									fileFormat:
										value === "audio" ? undefined : exportOptions.fileFormat,
								})
							}
							value={exportOptions.format}
						>
							<div className="space-y-3">
								{contentOptions.map((option) => (
									<div
										className="flex items-center space-x-3"
										key={option.value}
									>
										<Radio
											style={{
												color: "var(--color-primary-500)",
											}}
											value={option.value}
										/>
										<div className="flex items-center space-x-2">
											<span>{option.label}</span>
										</div>
									</div>
								))}
							</div>
						</Radio.Group>
					</div>

					{/* 文件格式选择 - 仅在选择倾听内容时显示 */}
					{exportOptions.format !== "audio" && (
						<div className="space-y-3">
							<h4 className="font-medium text-gray-700">文件格式</h4>
							<div className="flex space-x-3">
								{fileFormatOptions.map((option) => (
									<button
										className={`flex-1 rounded-lg border px-4 py-3 text-center transition-all ${
											exportOptions.fileFormat === option.value
												? "border-2 text-white"
												: "border-gray-300 text-gray-700 hover:border-gray-400"
										}`}
										key={option.value}
										onClick={() =>
											setExportOptions({
												...exportOptions,
												fileFormat: option.value as "docx" | "pdf",
											})
										}
										style={{
											backgroundColor:
												exportOptions.fileFormat === option.value
													? "var(--color-primary-500)"
													: "transparent",
											borderColor:
												exportOptions.fileFormat === option.value
													? "var(--color-primary-500)"
													: undefined,
										}}
										type="button"
									>
										{option.label}
									</button>
								))}
							</div>
						</div>
					)}

					{/* 操作按钮 */}
					<div className="flex space-x-3 pt-4">
						<button
							className="flex-1 rounded-lg border border-gray-300 px-4 py-3 text-gray-700 transition-colors hover:bg-gray-50"
							onClick={onClose}
							type="button"
						>
							取消
						</button>
						<button
							className="flex-1 rounded-lg px-4 py-3 text-white transition-colors"
							onClick={handleExport}
							onMouseEnter={(e) => {
								e.currentTarget.style.backgroundColor =
									"var(--color-primary-600)";
							}}
							onMouseLeave={(e) => {
								e.currentTarget.style.backgroundColor =
									"var(--color-primary-500)";
							}}
							style={{
								backgroundColor: "var(--color-primary-500)",
							}}
							type="button"
						>
							导出文件
						</button>
					</div>
				</div>
			),
		},
	];

	return (
		<ActionSheet
			actions={actions}
			closeOnAction={false}
			onClose={onClose}
			visible={visible}
		/>
	);
}
