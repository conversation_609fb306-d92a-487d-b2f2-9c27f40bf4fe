import { Modal, Toast } from "antd-mobile";
import CopyToClipboard from "react-copy-to-clipboard";

interface ExportSuccessModalProps {
	visible: boolean;
	fileName: string;
	fileUrl: string;
	onClose: () => void;
	onDownload: () => void;
}

const ExportSuccessModal: React.FC<ExportSuccessModalProps> = ({
	visible,
	fileName,
	fileUrl,
	onClose,
	onDownload,
}) => {
	const handleCopySuccess = () => {
		onClose();
		Toast.show({
			content: "复制成功",
		});
	};

	return (
		<Modal
			closeOnMaskClick={true}
			content={
				<div className="p-5 text-center">
					<h3 className="mb-4 text-gray-800 text-lg">导出成功</h3>
					<p className="mb-5 text-center text-gray-600 text-sm">
						文件名: {fileName}
					</p>

					<div className="mb-4 flex w-full justify-between space-x-2">
						<CopyToClipboard onCopy={handleCopySuccess} text={fileUrl}>
							<button
								className="rounded bg-gray-100 px-4 py-2 text-gray-700 hover:bg-gray-200"
								type="button"
							>
								复制链接
							</button>
						</CopyToClipboard>
						<button
							className="flex-1 rounded bg-blue-500 p-3 text-center text-white"
							onClick={onDownload}
							type="button"
						>
							下载文件
						</button>
					</div>
				</div>
			}
			onClose={onClose}
			visible={visible}
		/>
	);
};

export default ExportSuccessModal;
