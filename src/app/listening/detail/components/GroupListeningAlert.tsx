import type React from "react";

interface GroupListeningAlertProps {
	type?: number;
	status?: number;
}

const GroupListeningAlert: React.FC<GroupListeningAlertProps> = ({
	type,
	status,
}) => {
	// 只在小组倾听模式且状态为6时显示
	if (type !== 2 || status !== 6) {
		return null;
	}

	return (
		<div className="mt-2 mb-2 p-3 bg-orange-50 border border-orange-200 rounded-lg shadow-sm">
			<div className="flex items-start space-x-2">
				<div className="flex-shrink-0 mt-0.5">
					<svg
						className="w-5 h-5 text-orange-500"
						fill="currentColor"
						viewBox="0 0 20 20"
					>
						<title>Warning</title>
						<path
							fillRule="evenodd"
							d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
							clipRule="evenodd"
						/>
					</svg>
				</div>
				<div className="flex-1">
					<h3 className="text-sm font-medium text-orange-800">
						需要手动修改发言人身份
					</h3>
					<p className="mt-1 text-sm text-orange-700">
						小组倾听模式下转写已完成，需要手动修改发言人身份后才能生成倾听分析
					</p>
				</div>
			</div>
		</div>
	);
};

export default GroupListeningAlert;
