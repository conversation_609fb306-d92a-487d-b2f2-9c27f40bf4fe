/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
import { Modal, Toast } from "antd-mobile";
import Image from "next/image";
import { useEffect } from "react";
import { getMessage, savePictureToAlbum } from "@/utils";

interface ImagePreviewModalProps {
	visible: boolean;
	imageUrl: string;
	imageName: string;
	onClose: () => void;
}

const ImagePreviewModal: React.FC<ImagePreviewModalProps> = ({
	visible,
	imageUrl,
	imageName,
	onClose,
}) => {
	// 获取到RN的通知
	const onMessage = (event: any) => {
		try {
			const data = JSON.parse(event.data);
			console.log("🚀 ~ data:", data);
			if ("savePictureToAlbum" in data) {
				if (data.savePictureToAlbum) {
					Toast.show({
						content: "保存成功",
					});
				} else {
					Toast.show({
						content: "保存失败",
					});
				}
			}
			console.log("获取到RN的通知 onMessage ", JSON.stringify(data));
		} catch (_error) {}
	};
	useEffect(() => {
		getMessage(onMessage);

		// 清理函数，移除事件监听器
		return () => {
			window.removeEventListener("message", onMessage, false);
			document.removeEventListener("message", onMessage, false);
		};
	}, [onMessage]);
	return (
		<Modal
			closeOnMaskClick={true}
			onClose={onClose}
			visible={visible}
			content={
				<div className="flex flex-col h-[150vh] max-h-[800px]">
					{/* 固定的关闭按钮区域 */}
					<div className="flex-shrink-0 mb-2 flex items-center justify-between">
						<button
							className="rounded-full p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-600"
							onClick={onClose}
							type="button"
						>
							<svg
								className="h-5 w-5"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<title>Close</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M6 18L18 6M6 6l12 12"
								/>
							</svg>
						</button>
					</div>

					{/* 可滚动的图片区域 */}
					<div className="flex-1 overflow-auto rounded-lg">
						<Image
							alt={imageName}
							className="h-auto w-full object-contain"
							height={0}
							sizes="150vw"
							src={imageUrl}
							width={0}
						/>
					</div>

					{/* 固定的下载按钮区域 */}
					<div className="flex-shrink-0 mt-2 text-center">
						<button
							className="rounded-lg bg-blue-500 px-6 py-2 text-white transition-colors hover:bg-blue-600"
							onClick={() => {
								savePictureToAlbum(imageUrl);
							}}
							type="button"
						>
							下载图片
						</button>
					</div>
				</div>
			}
		/>
	);
};

export default ImagePreviewModal;
