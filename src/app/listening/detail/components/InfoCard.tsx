"use client";

import type { ListenRecodeDetailResponse } from "@/api/listening";

type InfoCardProps = {
	listenInfo: ListenRecodeDetailResponse;
	onAddTagClick: () => void;
	onRemoveTag: (tagId: string) => void;
	canEdit: boolean;
};

const tagColors = [
	"bg-green-50 text-green-700",
	"bg-orange-50 text-orange-700",
	"bg-purple-50 text-purple-700",
	"bg-blue-50 text-blue-700",
	"bg-pink-50 text-pink-700",
];

export default function InfoCard({
	listenInfo,
	onAddTagClick,
	onRemoveTag,
	canEdit,
}: InfoCardProps) {
	// 渲染学生信息
	const renderStudents = () => {
		const students = listenInfo.listening?.studentInfos || [];

		if (!students || students.length === 0) {
			// 兜底显示单个学生信息
			return (
				<span className="text-base text-gray-900">
					学生：{listenInfo.listening?.studentInfo?.roleName || ""}
				</span>
			);
		}

		if (students.length === 1) {
			// 单个学生
			return (
				<span className="text-base text-gray-900">
					学生：{students[0]?.roleName}
				</span>
			);
		}

		if (students.length <= 3) {
			// 少量学生，直接展示名字
			return (
				<span className="text-base text-gray-900">
					学生：{students.map((s) => s.roleName).join("、")}
				</span>
			);
		}

		// 多个学生，用标签形式展示
		return (
			<div className="flex flex-col gap-1">
				<span className="text-base text-gray-900">学生：</span>
				<div className="flex flex-wrap gap-1">
					{students.map((student, index) => (
						<span
							key={student.roleId || index}
							className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs text-blue-700"
						>
							{student.roleName}
						</span>
					))}
				</div>
			</div>
		);
	};

	return (
		<div className="rounded-lg bg-white p-4 shadow-sm">
			{/* 第一行：学生和班级 */}
			<div className="mb-3 flex items-start justify-between">
				<div className="flex-1 mr-4">{renderStudents()}</div>
				<div className="text-base text-gray-900 flex-shrink-0">
					班级：{listenInfo.listening?.deptName || "小3班"}
				</div>
			</div>

			{/* 第二行：日期和教师 */}
			<div className="mb-4 flex items-center justify-between">
				<div className="text-base text-gray-900">
					日期：
					{listenInfo.listening?.startTime
						? new Date((listenInfo.listening.startTime as number) * 1000)
								.toLocaleDateString("zh-CN", {
									year: "numeric",
									month: "2-digit",
									day: "2-digit",
								})
								.replace(/\//g, "-")
						: "2025-06-17"}
				</div>
				<div className="text-base text-gray-900">
					教师：{listenInfo.listening?.staffInfo?.roleName || "Alex"}
				</div>
			</div>

			{/* 标签部分保持不变 */}
			{listenInfo.tags && listenInfo.tags.length > 0 ? (
				<div className="flex flex-wrap gap-2 items-center">
					{listenInfo.tags?.map((item) => (
						<span
							key={item.tagId || item.name}
							className={`flex items-center rounded-full px-3 py-1 text-sm ${tagColors[Math.floor(Math.random() * tagColors.length)]}`}
						>
							#{item.name}
							{canEdit && (
								<button
									className="ml-2 text-sm hover:text-red-600 transition-colors"
									onClick={() => onRemoveTag(item.tagId || item.name)}
									type="button"
									title="删除标签"
								>
									×
								</button>
							)}
						</span>
					))}
					{canEdit && (
						<button
							onClick={onAddTagClick}
							className="flex items-center rounded-full border-2 border-dashed border-gray-300 px-3 py-1 text-sm text-gray-600 hover:border-blue-500 hover:text-blue-600 transition-colors"
							type="button"
						>
							+ 添加标签
						</button>
					)}
				</div>
			) : (
				<div className="flex items-center">
					{canEdit && (
						<button
							onClick={onAddTagClick}
							className="flex items-center rounded-full border-2 border-dashed border-gray-300 px-3 py-1 text-sm text-gray-600 hover:border-blue-500 hover:text-blue-600 transition-colors"
							type="button"
						>
							+ 添加标签
						</button>
					)}
				</div>
			)}
		</div>
	);
}
