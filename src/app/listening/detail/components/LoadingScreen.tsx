import { onBackPressed } from "@/utils";

interface LoadingScreenProps {
	title?: string;
	loadingText?: string;
	showBackButton?: boolean;
	onBack?: () => void;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
	title = "倾听详情",
	loadingText = "加载中...",
	showBackButton = true,
	onBack = onBackPressed,
}) => {
	return (
		<div className="flex min-h-screen flex-col bg-gray-50">
			{/* 顶部导航栏 - 固定在顶部 */}
			<div className="fixed top-0 right-0 left-0 z-50 border-gray-100 border-b bg-white">
				<div className="flex h-14 items-center justify-between px-4 py-3">
					{showBackButton ? (
						<button
							className="flex items-center justify-center p-2"
							onClick={onBack}
							type="button"
						>
							<picture>
								<source
									media="(prefers-color-scheme: dark)"
									srcSet="/images/live/arrow_left_dark.png"
								/>
								<img
									alt="返回"
									className="size-6 object-contain"
									src="/images/live/arrow_left.png"
								/>
							</picture>
						</button>
					) : (
						<div className="w-12" />
					)}
					<h1 className="absolute left-1/2 transform -translate-x-1/2 font-medium text-black text-lg">
						{title}
					</h1>
					<div className="w-12" /> {/* 占位元素保持居中 */}
				</div>
				<div className="flex h-screen flex-1 flex-col items-center items-center justify-center justify-center pt-flex">
					<div className="flex flex-col items-center space-y-4">
						<div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-500 border-t-transparent" />
						<span className="text-gray-500 text-sm">{loadingText}</span>
					</div>
				</div>
			</div>
		</div>
	);
};

export default LoadingScreen;
