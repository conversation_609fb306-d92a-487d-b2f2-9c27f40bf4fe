"use client";

import Image from "next/image";
import { useRef } from "react";
import type { ListeningMaterial } from "@/api/listening";

type MaterialsSectionProps = {
	materials?: ListeningMaterial[];
	onPreview: (item: ListeningMaterial) => void;
	onFileUpload?: (files: FileList) => void;
	onDelete?: (item: ListeningMaterial, index: number) => void;
	canUpload?: boolean;
	uploadingFiles?: { [key: string]: { progress: number; uploading: boolean } };
};

export default function MaterialsSection({
	materials,
	onPreview,
	onFileUpload,
	onDelete,
	canUpload = false,
	uploadingFiles = {},
}: MaterialsSectionProps) {
	const fileInputRef = useRef<HTMLInputElement>(null);

	const handleInputClick = () => {
		fileInputRef.current?.click();
	};

	const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		const files = event.target.files;
		if (files && files.length > 0 && onFileUpload) {
			onFileUpload(files);
		}
		// 清空input值，允许重复选择同一文件
		event.target.value = "";
	};

	const hasUploadingFiles = Object.keys(uploadingFiles).length > 0;

	return (
		<div className="mt-2 rounded-lg bg-white p-4 shadow-sm">
			<div className="flex items-center">
				<div className="mr-2 h-4 w-1 rounded bg-blue-500" />
				<h2 className="font-semibold text-gray-900 text-lg">作品</h2>
			</div>

			{/* 隐藏的文件输入 */}
			<input
				ref={fileInputRef}
				type="file"
				accept="image/*"
				multiple
				onChange={handleFileChange}
				className="hidden"
			/>

			{Array.isArray(materials) &&
				materials.length === 0 &&
				!hasUploadingFiles &&
				canUpload && (
					<div
						className="flex items-center justify-center rounded-lg border-2 border-gray-300 border-dashed bg-gray-50 p-8 mt-4 cursor-pointer hover:bg-gray-100 transition-colors"
						onClick={handleInputClick}
					>
						<div className="text-center">
							<div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-gray-200">
								<svg
									className="h-6 w-6 text-gray-400"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<title>Add</title>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
									/>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
									/>
								</svg>
							</div>
							<p className="text-gray-500 text-sm">点击添加活动表征图片</p>
							<p className="text-gray-400 text-xs mt-1">支持多张图片上传</p>
						</div>
					</div>
				)}
			{Array.isArray(materials) && materials.length === 0 && !canUpload && (
				<div className="mt-4">
					<p className="text-gray-500 text-sm">暂无活动表征图片</p>
				</div>
			)}

			{/* 显示上传中的文件 */}
			{hasUploadingFiles && (
				<div className="mt-4">
					{Object.entries(uploadingFiles).map(([fileId, { progress }]) => (
						<div
							key={fileId}
							className="mb-2 p-3 border border-gray-200 rounded-lg"
						>
							<div className="flex items-center justify-between mb-2">
								<span className="text-sm text-gray-600">上传中...</span>
								<span className="text-sm text-gray-500">
									{Math.round(progress)}%
								</span>
							</div>
							<div className="w-full bg-gray-200 rounded-full h-2">
								<div
									className="bg-blue-500 h-2 rounded-full transition-all duration-300"
									style={{ width: `${progress}%` }}
								/>
							</div>
						</div>
					))}
				</div>
			)}

			{Array.isArray(materials) && materials.length > 0 && (
				<>
					<div className="flex flex-wrap gap-2 mt-4">
						{materials.map((item, index) => {
							// 检查 URL 是否有效
							const isValidUrl = item.url?.startsWith("https://");
							return (
								<div
									key={item.id ?? item.name ?? index}
									className="relative w-full"
								>
									<button
										className="block w-full"
										onClick={() => onPreview(item)}
										type="button"
									>
										{isValidUrl ? (
											<Image
												alt={item.name || ""}
												className="h-[200px] w-full rounded-lg bg-gray-200 object-cover"
												height={200}
												sizes="100vw"
												src={item.url || ""}
												width={0}
												onError={(_e) => {
													console.error("Image load error:", item.url);
													// 可以设置一个默认图片或隐藏图片
												}}
											/>
										) : (
											<div className="h-[200px] w-full rounded-lg bg-gray-200 flex items-center justify-center">
												<span className="text-gray-500 text-sm">
													无效的图片链接
												</span>
											</div>
										)}
									</button>
									{/* 删除按钮 */}
									{canUpload && (
										<button
											type="button"
											className="absolute -top-2 -right-2 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-white shadow-md hover:bg-red-600 transition-colors z-10"
											onClick={(e) => {
												e.stopPropagation();
												if (onDelete) onDelete(item, index);
											}}
										>
											<svg
												width="12"
												height="12"
												viewBox="0 0 24 24"
												fill="none"
												xmlns="http://www.w3.org/2000/svg"
											>
												<title>Delete</title>
												<path
													d="M5 12H19"
													stroke="white"
													strokeWidth="2"
													strokeLinecap="round"
												/>
											</svg>
										</button>
									)}
								</div>
							);
						})}
					</div>

					{/* 添加更多图片按钮 */}
					{canUpload && (
						<button
							onClick={handleInputClick}
							className="mt-4 w-full flex items-center justify-center rounded-lg border-2 border-gray-300 border-dashed bg-gray-50 p-4 hover:bg-gray-100 transition-colors"
							type="button"
						>
							<div className="text-center">
								<div className="mx-auto mb-1 flex h-8 w-8 items-center justify-center rounded-full bg-gray-200">
									<svg
										className="h-4 w-4 text-gray-400"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<title>Add</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M12 4v16m8-8H4"
										/>
									</svg>
								</div>
								<p className="text-gray-500 text-sm">添加更多图片</p>
							</div>
						</button>
					)}
				</>
			)}
		</div>
	);
}
