"use client";

import { ActionSheet } from "antd-mobile";
import { Copy, Download, Edit, Share2, Trash2 } from "lucide-react";

interface MoreActionProps {
	visible: boolean;
	onClose: () => void;
	onShare?: () => void;
	onExport?: () => void;
	onEdit?: () => void;
	onDelete?: () => void;
	onCopy?: () => void;
	canEdit?: boolean;
	canDelete?: boolean;
}

export default function MoreAction({
	visible,
	onClose,
	onShare,
	onExport,
	onEdit,
	onDelete,
	onCopy,
	canEdit = true,
	canDelete = true,
}: MoreActionProps) {
	const actions = [
		{
			key: "share",
			text: (
				<div className="flex items-center space-x-3 py-2">
					<Share2 className="h-5 w-5 text-blue-500" />
					<span>分享</span>
				</div>
			),
			onClick: onShare,
		},
		{
			key: "export",
			text: (
				<div className="flex items-center space-x-3 py-2">
					<Download className="h-5 w-5 text-green-500" />
					<span>导出</span>
				</div>
			),
			onClick: onExport,
		},
		{
			key: "copy",
			text: (
				<div className="flex items-center space-x-3 py-2">
					<Copy className="h-5 w-5 text-purple-500" />
					<span>复制链接</span>
				</div>
			),
			onClick: onCopy,
		},
	];

	// 添加编辑选项
	if (canEdit && onEdit) {
		actions.push({
			key: "edit",
			text: (
				<div className="flex items-center space-x-3 py-2">
					<Edit className="h-5 w-5 text-orange-500" />
					<span>编辑</span>
				</div>
			),
			onClick: onEdit,
		});
	}

	// 添加删除选项
	if (canDelete && onDelete) {
		actions.push({
			key: "delete",
			text: (
				<div className="flex items-center space-x-3 py-2">
					<Trash2 className="h-5 w-5 text-red-500" />
					<span className="text-red-500">删除</span>
				</div>
			),
			onClick: onDelete,
		});
	}

	return (
		<ActionSheet
			actions={actions}
			cancelText="取消"
			onClose={onClose}
			visible={visible}
		/>
	);
}
