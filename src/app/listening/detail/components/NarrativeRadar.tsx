/** biome-ignore-all lint/a11y/useSemanticElements: <explanation> */
/** biome-ignore-all lint/complexity/noUselessFragments: <explanation> */
"use client";

import {
	Chart as ChartJS,
	type ChartOptions,
	Filler,
	Legend,
	LineElement,
	PointElement,
	RadialLinearScale,
	Tooltip,
} from "chart.js";
import { EditIcon } from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { Radar } from "react-chartjs-2";
import type { NarrativeAnalysisMetrics } from "@/api/listening";

ChartJS.register(
	RadialLinearScale,
	PointElement,
	LineElement,
	Filler,
	Tooltip,
	Legend,
);

type NarrativeRadarProps = {
	type: 1 | 2;
	canEdit: boolean;
	metrics?: NarrativeAnalysisMetrics[];
	interpretationText?: string;
	onEdit: () => void;
};

export default function NarrativeRadar({
	type,
	canEdit,
	metrics,
	interpretationText,
	onEdit,
}: NarrativeRadarProps) {
	const [popoverVisible, setPopoverVisible] = useState(false);
	const [popoverData, setPopoverData] = useState<any>(null);
	const [popoverPosition, setPopoverPosition] = useState({ x: 0, y: 0 });
	const popoverRef = useRef<HTMLDivElement>(null);

	// 点击外部区域关闭popover
	useEffect(() => {
		if (!popoverVisible) return;

		const handleClickOutside = (event: MouseEvent) => {
			if (
				popoverRef.current &&
				!popoverRef.current.contains(event.target as Node)
			) {
				setPopoverVisible(false);
			}
		};

		// 延迟添加事件监听器，避免立即触发
		const timer = setTimeout(() => {
			document.addEventListener("click", handleClickOutside);
		}, 100);

		return () => {
			clearTimeout(timer);
			document.removeEventListener("click", handleClickOutside);
		};
	}, [popoverVisible]);

	const hasMetrics = Array.isArray(metrics) && metrics.length > 0;

	// 开关：仅当设置了 NEXT_PUBLIC_ENABLE_NARRATIVE_RADAR_MOCK=true 且 metrics 为空时启用 mock
	const USE_MOCK =
		process.env.NEXT_PUBLIC_ENABLE_NARRATIVE_RADAR_MOCK === "true";

	// mock 数据：仅在开发演示用
	const mockMetrics = useMemo<NarrativeAnalysisMetrics[]>(
		() => [
			{
				code: "lang",
				name: "语言表达",
				score: 78,
				subMetrics: [
					{ code: "vocab", name: "词汇丰富度", score: 80 },
					{ code: "fluency", name: "表达流利度", score: 75 },
				],
			},
			{
				code: "logic",
				name: "逻辑组织",
				score: 72,
				subMetrics: [
					{ code: "structure", name: "结构清晰度", score: 70 },
					{ code: "coherence", name: "连贯性", score: 74 },
				],
			},
			{
				code: "emotion",
				name: "情绪表达",
				score: 65,
				subMetrics: [{ code: "tone", name: "语气与情感", score: 66 }],
			},
			{ code: "interaction", name: "互动配合", score: 60 },
			{ code: "content", name: "内容理解", score: 82 },
		],
		[],
	);

	// 优先使用后端数据；若无并且开启了 mock，则用 mock 数据
	const finalMetrics = hasMetrics
		? (metrics as NarrativeAnalysisMetrics[])
		: USE_MOCK
			? mockMetrics
			: undefined;
	const hasFinalMetrics =
		Array.isArray(finalMetrics) && finalMetrics.length > 0;

	const labels = useMemo(
		() =>
			hasFinalMetrics
				? (finalMetrics as NarrativeAnalysisMetrics[]).map((m) => m.name)
				: [],
		[hasFinalMetrics, finalMetrics],
	);
	const dataValues = useMemo(
		() =>
			hasFinalMetrics
				? (finalMetrics as NarrativeAnalysisMetrics[]).map((m) => m.score ?? 0)
				: [],
		[hasFinalMetrics, finalMetrics],
	);

	const finalInterpretation =
		interpretationText ||
		(USE_MOCK
			? "学生在语言表达和逻辑组织方面表现较好，建议继续加强情绪表达与互动配合能力。"
			: "");

	const radarData = useMemo(
		() => ({
			labels,
			datasets: [
				{
					data: dataValues,
					backgroundColor: "rgba(59, 130, 246, 0.3)",
					borderColor: "#3b82f6",
					borderWidth: 2,
					pointBackgroundColor: "#3b82f6",
					pointBorderColor: "#ffffff",
					pointBorderWidth: 2,
					pointRadius: 4,
				},
			],
		}),
		[labels, dataValues],
	);

	const maxScore = useMemo(() => {
		if (!hasFinalMetrics) return 100;
		const maxValue = Math.max(...dataValues);
		if (maxValue <= 100) return 100;
		return Math.ceil(maxValue / 20) * 20;
	}, [hasFinalMetrics, dataValues]);

	const radarOptions: ChartOptions<"radar"> = {
		responsive: true,
		maintainAspectRatio: false,
		plugins: { legend: { display: false }, tooltip: { enabled: false } },
		events: ["click"],
		scales: {
			r: {
				beginAtZero: true,
				min: 0,
				max: maxScore,
				ticks: { stepSize: 20, display: false },
				pointLabels: { color: "#4b5563", font: { size: 12 } },
				grid: { color: "#e5e7eb", lineWidth: 1 },
				angleLines: { color: "#e5e7eb", lineWidth: 1 },
			},
		},
		onClick: (event) => {
			const chart = (event as any).chart;
			const rect = chart.canvas.getBoundingClientRect();
			const x = (event as any).native.clientX - rect.left;
			const y = (event as any).native.clientY - rect.top;
			const centerX = rect.width / 2;
			const centerY = rect.height / 2;
			const radius = Math.min(centerX, centerY) * 0.8;
			const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);

			if (!hasFinalMetrics || distance > radius || !labels.length) return;

			// 1) 优先用 Chart.js 命中测试找到最近的点索引
			let targetIndex: number | null = null;
			try {
				const hitElements = chart.getElementsAtEventForMode(
					(event as any).native,
					"nearest",
					{ intersect: true },
					true,
				);
				if (hitElements && hitElements.length > 0) {
					targetIndex = hitElements[0].index;
				}
			} catch {
				// 忽略异常，走角度推断
			}

			// 2) 若未命中具体点，则用角度推断到最近维度
			if (targetIndex === null || targetIndex === undefined) {
				const angle = Math.atan2(y - centerY, x - centerX); // -PI..PI，0 在 +X 方向
				// ChartJS 雷达默认起始在顶部（-PI/2），顺时针
				let fromTop = angle + Math.PI / 2;
				if (fromTop < 0) fromTop += Math.PI * 2;
				const step = (Math.PI * 2) / labels.length;
				targetIndex = Math.round(fromTop / step) % labels.length;
			}

			if (targetIndex === null || targetIndex === undefined) return;

			const metric = (finalMetrics as NarrativeAnalysisMetrics[])[targetIndex];
			if (!metric) return;

			// 仅构造该维度及其子指标
			const items = [
				{
					label: metric.name,
					value: metric.score ?? 0,
					color: "#3b82f6",
					level: 0,
				},
				...(metric.subMetrics?.map((sm) => ({
					label: sm.name,
					value: sm.score ?? 0,
					color: "#60a5fa",
					level: 1,
				})) ?? []),
			];

			setPopoverData({ title: "本次表现", items });
			setPopoverPosition({ x, y });
			setPopoverVisible(true);
		},
	};

	return (
		<div className="mt-2 rounded-lg bg-white p-4 shadow-sm">
			<div className="relative">
				<>
					{type === 1 ? (
						<div className="mb-4 flex items-center">
							<div className="mr-2 h-4 w-1 rounded bg-blue-500" />
							<h2 className="font-semibold text-gray-900 text-lg">
								叙事能力表现
							</h2>
						</div>
					) : (
						<div className="mb-4 flex items-center justify-between">
							<div className="flex items-center">
								<div className="mr-2 h-4 w-1 rounded bg-blue-500" />
								<h2 className="font-semibold text-gray-900 text-lg">
									综合分析解读
								</h2>
							</div>
							{canEdit && (
								<button
									className="text-gray-400 hover:text-gray-600"
									type="button"
									onClick={onEdit}
								>
									<EditIcon className="h-5 w-5" />
								</button>
							)}
						</div>
					)}
				</>
				{type === 1 && (
					<>
						{hasFinalMetrics ? (
							<div className="relative mx-auto h-64 w-64">
								<Radar data={radarData} options={radarOptions} />
								{popoverVisible && popoverData && (
									<div
										ref={popoverRef}
										className="absolute z-50 min-w-[160px] rounded-lg border border-gray-200 bg-white p-4 shadow-lg"
										style={{
											left: `${popoverPosition.x - 80}px`,
											top: `${popoverPosition.y - 120}px`,
											transform: "translateX(-50%)",
										}}
									>
										<div className="absolute left-1/2 -bottom-2 h-0 w-0 -translate-x-1/2 transform border-l-4 border-r-4 border-t-4 border-transparent border-t-white" />
										<div className="absolute left-1/2 -bottom-3 h-0 w-0 -translate-x-1/2 transform border-l-[5px] border-r-[5px] border-t-[5px] border-transparent border-t-gray-200" />
										<h4 className="mb-3 border-gray-100 border-b pb-2 font-medium text-gray-900 text-sm">
											{popoverData.title}
										</h4>
										<div className="space-y-2">
											{popoverData.items.map((item: any, idx: number) => (
												<div
													className="flex items-center justify-between"
													key={`${item.label}-${item.level}-${idx}`}
												>
													<div className="flex items-center space-x-2">
														{item.level === 0 && (
															<div
																className="h-2 w-2 rounded-full"
																style={{ backgroundColor: item.color }}
															/>
														)}
														<span
															className={`text-gray-700 ${item.level === 1 ? "pl-4 text-xs" : "text-sm"}`}
														>
															{item.level === 1
																? `• ${item.label}`
																: item.label}
														</span>
													</div>
													<span className="font-medium text-gray-900 text-sm">
														{item.value}
													</span>
												</div>
											))}
										</div>
									</div>
								)}
							</div>
						) : (
							<div className="mx-auto flex h-40 w-full max-w-sm items-center justify-center rounded-lg border border-gray-100 bg-gray-50">
								<span className="text-gray-400 text-sm">
									当前语音暂时无法分析相关能力表现
								</span>
							</div>
						)}

						{hasFinalMetrics && (
							<div className="text-center text-gray-500 text-xs">
								点击雷达图查看详细分析
							</div>
						)}
					</>
				)}
			</div>
			<div className="mt-4 bg-white">
				<div className="mb-4 rounded-lg border border-blue-200 bg-blue-50 p-3">
					{type === 1 && (
						<div className="mb-4 flex items-center justify-between">
							<div className="flex items-center">
								<h4 className="text-gray-900 text-lg">综合分析解读</h4>
							</div>
							{canEdit && (
								<button
									className="text-gray-400 hover:text-gray-600"
									type="button"
									onClick={onEdit}
								>
									<EditIcon className="h-5 w-5" />
								</button>
							)}
						</div>
					)}
					<p className="text-blue-800 text-sm leading-relaxed whitespace-pre-wrap">
						{finalInterpretation || "暂无综合分析解读"}
					</p>
				</div>
			</div>
		</div>
	);
}
