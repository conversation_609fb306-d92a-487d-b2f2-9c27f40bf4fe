"use client";

import { EditIcon } from "lucide-react";

type ObservationSectionProps = {
	summaryContents?: string;
	canEdit: boolean;
	onEdit: () => void;
};

export default function ObservationSection({
	summaryContents,
	canEdit,
	onEdit,
}: ObservationSectionProps) {
	return (
		<div className="mt-2 rounded-lg bg-white p-4 shadow-sm">
			<div className="mb-4 flex items-center justify-between">
				<div className="flex items-center">
					<div className="mr-2 h-4 w-1 rounded bg-blue-500" />
					<h2 className="font-semibold text-gray-900 text-lg">
						教师观察与解读
					</h2>
				</div>
				{canEdit && (
					<button
						className="text-gray-400 hover:text-gray-600"
						type="button"
						onClick={onEdit}
					>
						<EditIcon className="h-5 w-5" />
					</button>
				)}
			</div>

			<div className="mb-4 rounded-lg bg-blue-50 p-3">
				<div className="flex items-start space-x-2">
					<div className="mt-0.5 flex h-5 w-5 flex-shrink-0 items-center justify-center rounded-full bg-blue-500">
						<svg
							className="h-3 w-3 text-white"
							fill="currentColor"
							viewBox="0 0 20 20"
						>
							<title>Info</title>
							<path
								clipRule="evenodd"
								d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
								fillRule="evenodd"
							/>
						</svg>
					</div>
					<p className="text-blue-700 text-sm leading-relaxed">
						本段内容由AI初步生成，请您结合实际观察进行修改和确认。
					</p>
				</div>
			</div>

			<div className="space-y-4">
				<div className="text-gray-800 text-sm leading-relaxed whitespace-pre-wrap">
					{summaryContents || "暂无内容"}
				</div>
			</div>
		</div>
	);
}
