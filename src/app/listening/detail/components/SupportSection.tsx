"use client";

import { EditIcon } from "lucide-react";

type SupportSectionProps = {
	todos?: string;
	canEdit: boolean;
	onEdit: () => void;
};

export default function SupportSection({
	todos,
	canEdit,
	onEdit,
}: SupportSectionProps) {
	return (
		<div className="mt-2 rounded-lg bg-white p-4 shadow-sm">
			<div className="mb-4 flex items-center justify-between">
				<div className="mb-4 flex items-center">
					<div className="mr-2 h-4 w-1 rounded bg-blue-500" />
					<h2 className="font-semibold text-gray-900 text-lg">支持与措施</h2>
				</div>
				{canEdit && (
					<button
						className="text-gray-400 hover:text-gray-600"
						type="button"
						onClick={onEdit}
					>
						<EditIcon className="h-5 w-5" />
					</button>
				)}
			</div>

			<div className="space-y-4">
				<div>
					<p className="mb-2 font-medium text-gray-800 text-sm whitespace-pre-wrap">
						{todos || "暂无内容"}
					</p>
				</div>
			</div>
		</div>
	);
}
