"use client";

import { Input, Modal, Toast } from "antd-mobile";
import { useState } from "react";

interface TagInputModalProps {
	visible: boolean;
	onClose: () => void;
	onConfirm: (tagName: string) => void;
	existingTags?: { tagId: string; name: string }[];
}

const TagInputModal = ({
	visible,
	onClose,
	onConfirm,
	existingTags = [],
}: TagInputModalProps) => {
	const [tagName, setTagName] = useState("");
	const [loading, setLoading] = useState(false);

	const handleConfirm = async () => {
		const trimmedName = tagName.trim();

		if (!trimmedName) {
			Toast.show({ content: "标签名称不能为空" });
			return;
		}

		if (trimmedName.length > 10) {
			Toast.show({ content: "标签名称不能超过10个字符" });
			return;
		}

		// 检查标签是否已存在
		const existingTag = existingTags.find((tag) => tag.name === trimmedName);
		if (existingTag) {
			Toast.show({ content: "标签已存在" });
			return;
		}

		setLoading(true);
		try {
			await onConfirm(trimmedName);
			setTagName("");
			onClose();
		} catch (error) {
			console.error("添加标签失败:", error);
		} finally {
			setLoading(false);
		}
	};

	const handleClose = () => {
		setTagName("");
		onClose();
	};

	return (
		<Modal
			visible={visible}
			title="添加标签"
			onClose={handleClose}
			content={
				<div className="py-4">
					<Input
						value={tagName}
						onChange={setTagName}
						placeholder="请输入标签名称"
						maxLength={10}
						clearable
						onEnterPress={handleConfirm}
					/>
					<div className="mt-2 text-xs text-gray-500">最多10个字符</div>
				</div>
			}
			actions={[
				{
					key: "confirm",
					text: "确定",
					primary: true,
					onClick: handleConfirm,
				},
				{
					key: "cancel",
					text: "取消",
					onClick: handleClose,
				},
			]}
		/>
	);
};

export default TagInputModal;
