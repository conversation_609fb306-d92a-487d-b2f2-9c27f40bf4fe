"use client";

type TopBarProps = {
	title: string;
	status?: number;
	canRegenerate?: boolean;
	onBack: () => void;
	onExport: () => void;
	onRegenerate: () => void;
};

import { Download, RefreshCcwIcon } from "lucide-react";

export default function TopBar({
	title,
	canRegenerate,
	status,
	onBack,
	onExport,
	onRegenerate,
}: TopBarProps) {
	return (
		<header
			className="fixed top-0 right-0 left-0 z-[70] border-gray-100 border-b bg-white"
			style={{ transform: "translateY(var(--vv-offset-top, 0px))" }}
		>
			<div className="relative flex h-14 items-center px-4 py-3">
				<button
					className="flex items-center justify-center p-2"
					onClick={onBack}
					type="button"
				>
					<picture>
						<source
							media="(prefers-color-scheme: dark)"
							srcSet="/images/live/arrow_left_dark.png"
						/>
						<img
							alt="返回"
							className="size-6 object-contain"
							src="/images/live/arrow_left.png"
						/>
					</picture>
				</button>

				<h1 className="absolute left-1/2 transform -translate-x-1/2 font-medium text-black text-lg">
					{title}
				</h1>

				<div className="ml-auto flex items-center gap-2">
					{(status === 5 || status === 6) && canRegenerate && (
						<button
							className="bg-white px-3 py-1.5 text-primary-500 text-sm"
							onClick={onRegenerate}
							type="button"
						>
							<RefreshCcwIcon className="size-5" />
						</button>
					)}
					<button
						className="bg-white px-3 py-1.5 text-primary-500 text-sm"
						onClick={onExport}
						type="button"
					>
						<Download className="size-5" />
					</button>
				</div>
			</div>
		</header>
	);
}
