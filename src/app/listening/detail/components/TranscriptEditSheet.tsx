"use client";

import { Checkbox, Input, Popup, Radio, TextArea, Toast } from "antd-mobile";
import type { RadioValue } from "antd-mobile/es/components/radio";
import { useEffect, useState } from "react";
import type {
	ListenRecodeDetailResponse,
	TranscriptSegment,
} from "@/api/listening";

interface TranscriptEditSheetProps {
	initialGlobalUpdate: boolean;
	visible: boolean;
	onClose: () => void;
	transcript: TranscriptSegment | null;
	listenInfo: ListenRecodeDetailResponse;
	onSave: (data: {
		speakerRole: number;
		speakerLabel: string;
		speakerId: string;
		transcriptText: string;
		globalUpdate: boolean; // 新增：是否全局更新发言人标签
	}) => void;
	loading?: boolean;
}

const TranscriptEditSheet = ({
	initialGlobalUpdate,
	visible,
	onClose,
	transcript,
	listenInfo,
	onSave,
	loading = false,
}: TranscriptEditSheetProps) => {
	const [speakerRole, setSpeakerRole] = useState<number>(1);
	const [speakerLabel, setSpeakerLabel] = useState("");
	const [transcriptText, setTranscriptText] = useState("");
	const [speakerId, setSpeakerId] = useState<string>("");
	const [selectedStudentId, setSelectedStudentId] = useState<string>("");
	const [globalUpdate, setGlobalUpdate] = useState<boolean>(false); // 新增：全局更新状态

	// 获取学生列表
	const students = listenInfo.listening?.studentInfos || [];
	const teacher = listenInfo.listening?.staffInfo;

	// 当transcript变化时，更新表单数据
	useEffect(() => {
		if (transcript) {
			setSpeakerRole(transcript.speakerRole || 0);
			setTranscriptText(transcript.transcriptText || "");
			setSpeakerId(transcript.speakerId || "");

			// 根据speakerRole设置默认的speakerLabel
			const defaultLabel = transcript.speakerLabel || "";

			setSpeakerLabel(defaultLabel);

			// 如果是学生角色，尝试匹配对应的学生ID
			if (transcript.speakerRole === 2 && transcript.speakerId) {
				const matchedStudent = students.find(
					(s) => s.roleId === transcript.speakerId,
				);
				setSelectedStudentId(matchedStudent?.roleId || "");
			}
			setGlobalUpdate(initialGlobalUpdate);
		}
	}, [transcript, students, teacher, initialGlobalUpdate]);

	// 当选择身份时，自动设置对应的标签
	const handleRoleChange = (role: RadioValue) => {
		setSpeakerRole(Number(role));

		if (role === 1) {
			// 选择老师
			setSpeakerLabel(teacher?.roleName || "");
			setSpeakerId(teacher?.roleId || "");
			setSelectedStudentId("");
		} else if (role === 2) {
			// 选择学生，如果只有一个学生则自动选中
			if (students.length === 1) {
				setSelectedStudentId(students[0]?.roleId || "");
				setSpeakerLabel(students[0]?.roleName || "");
				setSpeakerId(students[0]?.roleId || "");
			} else {
				// 多个学生时清空，等待用户选择
				setSelectedStudentId("");
				setSpeakerLabel("");
				setSpeakerId("");
			}
		}
	};

	// 当选择学生时，设置对应的标签
	const handleStudentChange = (studentId: RadioValue) => {
		setSelectedStudentId(studentId as string);
		const selectedStudent = students.find(
			(s) => s.roleId === (studentId as string),
		);
		setSpeakerLabel(selectedStudent?.roleName || "");
		setSpeakerId(selectedStudent?.roleId || "");
	};

	const handleSave = () => {
		// 验证必填字段
		if (!speakerRole) {
			Toast.show({ content: "请选择发言人身份" });
			return;
		}
		if (!speakerLabel.trim()) {
			Toast.show({ content: "发言人标签不能为空" });
			return;
		}

		if (!transcriptText.trim()) {
			Toast.show({ content: "对话内容不能为空" });
			return;
		}

		if (speakerLabel.trim().length > 16) {
			Toast.show({ content: "发言人标签不能超过16个字符" });
			return;
		}

		// 如果选择了学生角色但没有选择具体学生
		if (speakerRole === 2 && students.length > 1 && !selectedStudentId) {
			Toast.show({ content: "请选择具体的学生" });
			return;
		}

		onSave({
			speakerId,
			speakerRole,
			speakerLabel: speakerLabel.trim(),
			transcriptText: transcriptText.trim(),
			globalUpdate, // 传递全局更新标志
		});
	};

	const handleClose = () => {
		if (!loading) {
			onClose();
		}
	};

	return (
		<Popup
			visible={visible}
			onMaskClick={handleClose}
			position="bottom"
			bodyStyle={{
				height: "70vh",
				borderTopLeftRadius: "12px",
				borderTopRightRadius: "12px",
				padding: "0",
			}}
		>
			<div className="flex flex-col h-full">
				{/* 头部 */}
				<div className="flex items-center justify-between p-4 border-b border-gray-200">
					<button
						className="text-gray-500 hover:text-gray-700"
						onClick={handleClose}
						disabled={loading}
						type="button"
					>
						取消
					</button>
					<h3 className="text-lg font-medium text-gray-900">编辑对话内容</h3>
					<button
						className="text-blue-500 hover:text-blue-700 font-medium disabled:text-gray-400"
						onClick={handleSave}
						disabled={loading}
						type="button"
					>
						{loading ? "保存中..." : "保存"}
					</button>
				</div>

				{/* 内容区域 */}
				<div className="flex-1 overflow-y-auto p-4 space-y-6">
					{/* 身份选择 */}
					<div>
						<h4 className="block text-sm font-medium text-gray-700 mb-3">
							身份
						</h4>
						<Radio.Group value={speakerRole} onChange={handleRoleChange}>
							<div className="flex flex-wrap gap-4">
								{teacher && (
									<Radio value={1} className="flex items-center">
										<span className="text-gray-900">
											老师 - {teacher.roleName}
										</span>
									</Radio>
								)}
								{students.length > 0 && (
									<Radio value={2} className="flex items-center">
										<span className="text-gray-900">学生</span>
									</Radio>
								)}
							</div>
						</Radio.Group>
					</div>

					{/* 学生选择（当选择学生身份且有多个学生时显示） */}
					{speakerRole === 2 && students.length > 0 && (
						<div>
							<h4 className="block text-sm font-medium text-gray-700 mb-3">
								选择学生
							</h4>
							<Radio.Group
								value={selectedStudentId}
								onChange={handleStudentChange}
							>
								<div className="flex flex-wrap gap-4">
									{students.map((student) => (
										<Radio
											key={student.roleId}
											value={student.roleId}
											className="items-center"
										>
											<span className="text-gray-900">{student.roleName}</span>
										</Radio>
									))}
								</div>
							</Radio.Group>
						</div>
					)}

					{/* 发言人标签 */}
					<div>
						<h4 className="block text-sm font-medium text-gray-700 mb-2">
							发言人标签
						</h4>
						<Input
							value={speakerLabel}
							onChange={setSpeakerLabel}
							placeholder="请选择发言人"
							maxLength={16}
							className="bg-gray-50"
							disabled={true}
						/>

						{/* 全局更新勾选项 */}
						<div className="mt-3">
							<Checkbox checked={globalUpdate} onChange={setGlobalUpdate}>
								<span className="text-sm text-gray-600">
									同时更新该发言人在本次倾听记录中的所有标签
								</span>
							</Checkbox>
						</div>
					</div>

					{/* 对话内容 */}
					<div>
						<h4 className="block text-sm font-medium text-gray-700 mb-2">
							对话内容
						</h4>
						<TextArea
							value={transcriptText}
							onChange={setTranscriptText}
							placeholder="请输入对话内容"
							rows={8}
							className="bg-gray-50"
							onFocus={(e) => {
								setTimeout(() => {
									e.target.scrollIntoView({
										behavior: "smooth",
										block: "center",
									});
								}, 100);
							}}
						/>
					</div>
				</div>
			</div>
		</Popup>
	);
};

export default TranscriptEditSheet;
