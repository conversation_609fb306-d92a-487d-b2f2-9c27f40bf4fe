/** biome-ignore-all lint/a11y/useSemanticElements: <explanation> */
/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
"use client";
import { EditIcon } from "lucide-react";
import type { TranscriptSegment } from "@/api/listening";

type SuspiciousWord = {
	suggestionId: string;
	word: string;
	startIndex: number;
	endIndex: number;
	suggestions: string[];
};
export type SuspiciousWordsMap = {
	[transcriptId: string]: SuspiciousWord[];
};

type TranscriptListProps = {
	transcripts: TranscriptSegment[];
	canEdit: boolean;
	onToggleSpeakerTextEditing: (id: string) => void;
	onJumpAudio: (item: any) => void;
	suspiciousWords: SuspiciousWordsMap;
	onWordClick: (arg: {
		transcriptId: string;
		wordIndex: number;
		wordInfo: SuspiciousWord;
		anchor: DOMRect;
	}) => void;
};

const formatTime = (seconds: number) => {
	const mins = Math.floor(seconds / 60);
	const secs = Math.floor(seconds % 60);
	return `${mins}:${secs < 10 ? "0" : ""}${secs}`;
};

export default function TranscriptList({
	transcripts,
	canEdit,
	onToggleSpeakerTextEditing,
	onJumpAudio,
	suspiciousWords,
	onWordClick,
}: TranscriptListProps) {
	const renderTextWithHighlights = (text: string, transcriptId: string) => {
		const words = suspiciousWords[transcriptId] || [];
		if (!words.length) return <span>{text}</span>;

		const parts: JSX.Element[] = [];
		let lastIndex = 0;
		const sortedWords = [...words].sort((a, b) => a.startIndex - b.startIndex);

		sortedWords.forEach((wordInfo, wordIndex) => {
			if (wordInfo.startIndex > lastIndex) {
				parts.push(
					<span key={`text-${lastIndex}`}>
						{text.slice(lastIndex, wordInfo.startIndex)}
					</span>,
				);
			}

			parts.push(
				<span
					className="cursor-pointer rounded bg-yellow-200 px-1 transition-colors hover:bg-yellow-300"
					key={`highlight-${wordInfo.suggestionId}`}
					onClick={(e) => {
						e.stopPropagation();
						const rect = (
							e.currentTarget as HTMLSpanElement
						).getBoundingClientRect();
						onWordClick({
							transcriptId,
							wordIndex,
							wordInfo,
							anchor: rect,
						});
					}}
					onKeyDown={() => null}
					role="button"
					tabIndex={0}
				>
					{wordInfo.word}
				</span>,
			);

			lastIndex = wordInfo.endIndex + 1;
		});

		if (lastIndex < text.length) {
			parts.push(
				<span key={`text-${lastIndex}`}>{text.slice(lastIndex)}</span>,
			);
		}

		return <>{parts}</>;
	};

	const getAvatarColor = (speaker: string) => {
		if (speaker?.includes("李小欢") || speaker?.includes("欢"))
			return "bg-orange-500";
		if (speaker?.includes("徐志远") || speaker?.includes("远"))
			return "bg-primary-500";
		if (speaker?.includes("黄启源") || speaker?.includes("源"))
			return "bg-teal-500";
		return "bg-gray-500";
	};

	const getAvatarText = (speaker: string) => {
		if (speaker?.includes("李小欢") || speaker?.includes("欢")) return "欢";
		if (speaker?.includes("徐志远") || speaker?.includes("远")) return "远";
		if (speaker?.includes("黄启源") || speaker?.includes("源")) return "源";
		return speaker?.charAt(0) || "?";
	};

	return (
		<div className="space-y-4 mt-2 rounded-lg bg-white p-4 shadow-sm">
			<div className="mb-4 flex items-center">
				<div className="mr-2 h-4 w-1 rounded bg-primary-500" />
				<h2 className="font-semibold text-gray-900 text-lg">对话</h2>
			</div>
			{!transcripts || transcripts.length === 0 ? (
				<div className="flex items-center justify-center rounded-lg bg-gray-50 p-4">
					<div className="text-center">
						<div className="mx-auto mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-gray-200">
							<svg
								className="h-6 w-6 text-gray-400"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<title>对话</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M8 10h8M8 14h5M5 7h14a2 2 0 012 2v7a2 2 0 01-2 2H9l-4 3v-3H5a2 2 0 01-2-2V9a2 2 0 012-2z"
								/>
							</svg>
						</div>
						<p className="text-gray-500 text-sm">暂无对话内容</p>
					</div>
				</div>
			) : (
				transcripts.map((line, _idx) => (
					<div className="flex items-start space-x-3" key={line.id}>
						<div
							className={`flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full font-medium text-sm text-white ${getAvatarColor(line.speakerLabel || "")}`}
						>
							{getAvatarText(line.speakerLabel || "")}
						</div>

						<div className="flex-1">
							<div className="mb-1 flex items-center space-x-2">
								{/* 移除EditableText，直接显示speakerLabel */}
								<span className="font-medium text-gray-900 text-sm">
									{line.speakerLabel || ""}
								</span>
								<span className="text-gray-500 text-xs">
									{formatTime(Number(line.startOffset ?? 0) / 1000)}
								</span>
								{canEdit && (
									<button
										className="px-3 py-1 text-xs text-gray-400 hover:text-gray-600"
										type="button"
										onClick={(e) => {
											e.stopPropagation();
											onToggleSpeakerTextEditing(line.id);
										}}
									>
										<EditIcon className="h-4 w-4" />
									</button>
								)}
							</div>

							<div
								className="cursor-pointer rounded-2xl border border-gray-200 bg-white px-4 py-3 shadow-sm transition-shadow hover:shadow-md"
								onClick={() => onJumpAudio(line)}
								onKeyDown={() => null}
								role="button"
								tabIndex={0}
							>
								{/* 移除EditableText，直接显示transcriptText */}
								<div className="text-gray-800 text-sm leading-relaxed">
									{renderTextWithHighlights(line.transcriptText || "", line.id)}
								</div>
							</div>
						</div>
					</div>
				))
			)}
		</div>
	);
}
