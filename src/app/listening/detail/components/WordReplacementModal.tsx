/** biome-ignore-all lint/suspicious/noArrayIndexKey: <explanation> */
"use client";
import { Input, Popup } from "antd-mobile";
import { useState } from "react";

type WordReplacementModalProps = {
	visible: boolean;
	word: string;
	suggestions: string[];
	onClose: () => void;
	onWordReplace: (newWord: string) => void;
	onIgnore: () => void;
};

export default function WordReplacementModal({
	visible,
	word,
	suggestions,
	onClose,
	onWordReplace,
	onIgnore,
}: WordReplacementModalProps) {
	const [customInput, setCustomInput] = useState("");

	const handleWordSelection = (selectedWord: string) => {
		onWordReplace(selectedWord);
		onClose();
	};

	const handleCustomInput = () => {
		if (customInput.trim()) {
			onWordReplace(customInput.trim());
			setCustomInput("");
			onClose();
		}
	};

	const handleKeepOriginal = () => {
		onClose();
		onIgnore();
	};

	const handleModalClose = () => {
		setCustomInput("");
		onClose();
	};

	return (
		<Popup
			visible={visible}
			onMaskClick={handleModalClose}
			showCloseButton
			onClose={handleModalClose}
			position="bottom"
			bodyStyle={{
				borderTopLeftRadius: "12px",
				borderTopRightRadius: "12px",
				padding: "0",
			}}
		>
			<div className="flex-1 overflow-y-auto flex-col h-full">
				<div className="items-center text-center p-4 border-gray-200">
					<h3 className="mb-2 font-semibold text-gray-900 text-lg">替换建议</h3>
					<p className="text-gray-600 text-sm">
						当前词汇：
						<span className="rounded bg-yellow-200 px-2 py-1">{word}</span>
					</p>
				</div>

				<div className="space-y-2 p-4">
					<p className="mb-3 font-medium text-gray-700 text-sm">
						选择替换词汇：
					</p>
					{suggestions.map((suggestion, index) => (
						<button
							className="w-full rounded-lg border border-gray-200 px-4 py-3 text-left transition-colors hover:border-primary-300 hover:bg-primary-50"
							key={index}
							onClick={() => handleWordSelection(suggestion)}
							type="button"
						>
							<span className="text-gray-900">{suggestion}</span>
						</button>
					))}

					{/* 自定义输入区域 */}
					<div className="mt-4 space-y-2">
						<p className="font-medium text-gray-700 text-sm">或者手动输入：</p>
						<div className="flex gap-2">
							<Input
								type="text"
								value={customInput}
								onChange={(e) => setCustomInput(e)}
								placeholder="输入自定义词汇"
								className="rounded-lg border border-gray-200 px-3 py-2 text-sm focus:border-primary-300 focus:outline-none focus:ring-1 focus:ring-primary-300"
								onFocus={() => {
									setTimeout(() => {
										window.scrollTo({
											top: document.body.scrollHeight,
											behavior: "smooth",
										});
									}, 100);
								}}
							/>
							<button
								onClick={handleCustomInput}
								disabled={!customInput.trim()}
								className="rounded-lg bg-blue-500 px-3 py-2 text-white transition-colors hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
								style={{ fontSize: 15 }}
								type="button"
							>
								确认
							</button>
						</div>
					</div>

					<button
						className="mt-2 w-full rounded-lg border border-gray-200 px-4 py-3 text-left transition-colors hover:bg-gray-50"
						onClick={handleKeepOriginal}
						type="button"
					>
						<span className="text-gray-500">保持原词汇</span>
					</button>
				</div>
			</div>
		</Popup>
	);
}
