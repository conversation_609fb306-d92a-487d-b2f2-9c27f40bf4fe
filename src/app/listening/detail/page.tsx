"use client";
import { Dialog, Loading, Toast } from "antd-mobile";
import { useSearchParams } from "next/navigation";
import { useEffect, useLayoutEffect, useRef, useState } from "react";
import type {
	ListeningMaterial,
	ListenRecodeDetailResponse,
	TranscriptSegment,
} from "@/api/listening";
import {
	addTag,
	deleteTag,
	exportListeningRecode,
	getListeningDetail,
	handleSuggestion,
	reGenerate,
	updateListeningContent,
	updateListeningContextDescription,
	updateListeningInterpretation,
	updateListeningRecodeTranscriptSegments,
	updateListeningSupportMeasure,
	updateListenRecodeTranscriptAllSpeaker,
	updateListenRecodeTranscriptSpeaker,
	updateMaterials,
} from "@/api/listening";
import { useCommonStore } from "@/store/useCommonStore";
import { onBackPressed } from "@/utils";
// 在导入部分添加
import { generateKey, uploadObs } from "@/utils/obs";
import AudioPlayer from "./components/AudioPlayer";
import ContextDescriptionSection from "./components/ContextDescriptionSection";
import EditText from "./components/EditText";
import ExportFile, { type ExportOptions } from "./components/ExportFile";
import ExportSuccessModal from "./components/ExportSuccessModal";
import GroupListeningAlert from "./components/GroupListeningAlert";
import ImagePreviewModal from "./components/ImagePreviewModal";
import InfoCard from "./components/InfoCard";
import LoadingScreen from "./components/LoadingScreen";
import MaterialsSection from "./components/MaterialsSection";
import NarrativeRadar from "./components/NarrativeRadar";
import ObservationSection from "./components/ObservationSection";
import SupportSection from "./components/SupportSection";
// 在导入部分添加新组件
import TagInputModal from "./components/TagInputModal";
import TopBar from "./components/TopBar";
import TranscriptEditSheet from "./components/TranscriptEditSheet";
import TranscriptList, {
	type SuspiciousWordsMap,
} from "./components/TranscriptList";
import WordReplacementModal from "./components/WordReplacementModal";

const DetailScreen = () => {
	const listeningId = useSearchParams().get("listeningId") || "";

	// --- viewport state refs & helpers ---
	const kbOpenRef = useRef(false);
	const rafIdRef = useRef(0);

	// Freeze app height to layout viewport unless keyboard is open.
	const setAppHeightBase = () => {
		try {
			const h = typeof window !== "undefined" ? window.innerHeight : 0;
			document.documentElement.style.setProperty(
				"--app-height",
				`${Math.round(h)}px`,
			);
			document.documentElement.style.setProperty("--vv-offset-top", "0px");
			document.documentElement.style.setProperty("--vv-offset-left", "0px");
		} catch {}
	};

	// ---- Fix mobile viewport + keyboard jank (iOS/Android) ----
	const setAppHeight = () => {
		try {
			const vv =
				typeof window !== "undefined" ? window.visualViewport : undefined;
			// When keyboard is NOT open, stick to window.innerHeight to avoid address bar show/hide jank on iOS.
			const height =
				vv?.height && kbOpenRef.current ? vv.height : window.innerHeight;
			document.documentElement.style.setProperty(
				"--app-height",
				`${Math.round(height)}px`,
			);

			// Only trust visualViewport offsets when keyboard is open.
			const top =
				vv?.offsetTop && kbOpenRef.current ? Math.round(vv.offsetTop) : 0;
			const left =
				vv?.offsetLeft && kbOpenRef.current ? Math.round(vv.offsetLeft) : 0;
			document.documentElement.style.setProperty("--vv-offset-top", `${top}px`);
			document.documentElement.style.setProperty(
				"--vv-offset-left",
				`${left}px`,
			);
		} catch {}
	};

	// Keep CSS var --app-height in sync with visual viewport
	// 在状态管理部分添加上传状态
	const [uploadingFiles, setUploadingFiles] = useState<{
		[key: string]: { progress: number; uploading: boolean };
	}>({});

	useLayoutEffect(() => {
		// Set once on mount to avoid first-scroll jump on iOS as URL bar collapses.
		setAppHeightBase();
	}, []);

	useEffect(() => {
		if (typeof window === "undefined") return;

		const onChange = () => {
			if (rafIdRef.current) cancelAnimationFrame(rafIdRef.current);
			rafIdRef.current = requestAnimationFrame(() => {
				if (kbOpenRef.current) {
					// Only when keyboard open do we follow visualViewport.
					setAppHeight();
				} else {
					// Otherwise, keep height steady to prevent jank while address bar shows/hides.
					setAppHeightBase();
				}
			});
		};

		// Initialize once.
		onChange();

		// Window events
		window.addEventListener("resize", onChange, { passive: true } as any);
		window.addEventListener("orientationchange", onChange);
		// VisualViewport events (safe on supported browsers)
		window.visualViewport?.addEventListener("resize", onChange);
		window.visualViewport?.addEventListener("scroll", onChange);

		return () => {
			if (rafIdRef.current) cancelAnimationFrame(rafIdRef.current);
			window.removeEventListener("resize", onChange as any);
			window.removeEventListener("orientationchange", onChange as any);
			window.visualViewport?.removeEventListener("resize", onChange as any);
			window.visualViewport?.removeEventListener("scroll", onChange as any);
		};
	}, []);

	// Detect keyboard open/close and adjust bottom inset (audio bar padding) to avoid jump after dismiss
	useEffect(() => {
		if (typeof window === "undefined") return;
		const root = document.documentElement;
		const updateKeyboardState = () => {
			const vv = window.visualViewport;
			// Heuristic: if visual viewport is significantly smaller than layout viewport, keyboard is likely open
			const isKbOpen = !!vv && window.innerHeight - vv.height > 120; // px threshold

			kbOpenRef.current = !!isKbOpen;
			root.classList.toggle("kb-open", !!isKbOpen);
			// When keyboard opens, collapse audio player reserved space; restore when closed
			root.style.setProperty(
				"--audio-player-height",
				isKbOpen ? "0px" : "160px",
			);

			// Update height using the appropriate strategy.
			if (isKbOpen) {
				setAppHeight();
			} else {
				setAppHeightBase();
			}
		};
		updateKeyboardState();
		window.visualViewport?.addEventListener("resize", updateKeyboardState);
		window.visualViewport?.addEventListener("scroll", updateKeyboardState);
		return () => {
			window.visualViewport?.removeEventListener("resize", updateKeyboardState);
			window.visualViewport?.removeEventListener("scroll", updateKeyboardState);
		};
	}, []);

	// Restore scroll position after input blur to avoid sticky offset after keyboard hides (iOS Safari)
	useEffect(() => {
		if (typeof window === "undefined") return;
		let prevScrollY = 0;
		const root = document.documentElement;

		const isEditable = (el: EventTarget | null) => {
			const t = el as HTMLElement | null;
			if (!t) return false;

			const tag = (t.tagName || "").toUpperCase();

			// 原生可编辑元素
			if (tag === "TEXTAREA" || t.getAttribute("contenteditable") === "true") {
				return true;
			}

			// antd-mobile 组件识别
			if (
				t.classList.contains("adm-input") ||
				t.classList.contains("adm-text-area")
			) {
				return true;
			}

			// 通用可编辑元素识别
			if (
				t.getAttribute("role") === "textbox" ||
				t.hasAttribute("aria-multiline") ||
				t.querySelector('textarea, [contenteditable="true"]')
			) {
				return true;
			}

			return false;
		};
		const onFocusIn = (e: FocusEvent) => {
			if (isEditable(e.target)) {
				prevScrollY = window.scrollY;
				root.classList.add("kb-open");
			}
		};

		const onFocusOut = (e: FocusEvent) => {
			if (isEditable(e.target)) {
				// Defer to allow viewport to settle
				setTimeout(() => {
					root.classList.remove("kb-open");
					// Use auto to avoid animation
					window.scrollTo({ top: prevScrollY, left: 0, behavior: "auto" });
					setAppHeightBase();
				}, 60);
			}
		};

		document.addEventListener("focusin", onFocusIn);
		document.addEventListener("focusout", onFocusOut);
		return () => {
			document.removeEventListener("focusin", onFocusIn);
			document.removeEventListener("focusout", onFocusOut);
		};
	}, []);
	// ---- end viewport/keyboard fix ----

	const { userId } = useCommonStore();
	const [listenInfo, setListenInfo] = useState<ListenRecodeDetailResponse>({});
	const [isExporting, setIsExporting] = useState(false);
	const [isDataLoading, setIsDataLoading] = useState(true); // 新增：数据加载状态
	const [positionSeconds, setPositionSeconds] = useState(0);
	const [exportResult, setExportResult] = useState<{
		fileName: string;
		fileUrl: string;
		visible: boolean;
	}>({ fileName: "", fileUrl: "", visible: false });
	const [exportFileVisible, setExportFileVisible] = useState(false);
	const [isSummaryContentsEditing, setIsSummaryContentsEditing] =
		useState(false);
	const [isTodosEditing, setIsTodosEditing] = useState(false);
	const [isContextDescriptionEditing, setIsContextDescriptionEditing] =
		useState(false);
	const [isInterpretationTextEditng, setIsInterpretationTextEditng] =
		useState(false);

	const [suspiciousWords, setSuspiciousWords] = useState<SuspiciousWordsMap>(
		{},
	);

	const [replacementModal, setReplacementModal] = useState<{
		visible: boolean;
		transcriptId: string;
		wordIndex: number;
		word: string;
		suggestionId: string;
		suggestions: string[];
		position: { x: number; y: number };
	}>({
		visible: false,
		transcriptId: "",
		wordIndex: -1,
		word: "",
		suggestionId: "",
		suggestions: [],
		position: { x: 0, y: 0 },
	});

	const [imagePreview, setImagePreview] = useState<{
		visible: boolean;
		imageUrl: string;
		imageName: string;
	}>({
		visible: false,
		imageUrl: "",
		imageName: "",
	});

	// 在状态管理部分添加弹窗状态
	const [tagModalVisible, setTagModalVisible] = useState(false);
	const [transcriptEditSheet, setTranscriptEditSheet] = useState<{
		visible: boolean;
		transcript: TranscriptSegment | null;
		id: string;
	}>({ visible: false, transcript: null, id: "" });

	const [isTranscriptSaving, setIsTranscriptSaving] = useState(false);
	// 可疑词汇数据
	useEffect(() => {
		if (
			(listenInfo.segments || []).length > 0 &&
			listenInfo.listening?.staffInfo?.roleId === userId
		) {
			const mockSuspiciousWords: typeof suspiciousWords = {};
			listenInfo.segments?.forEach((transcript: TranscriptSegment) => {
				const text = transcript.transcriptText || "";
				const words: {
					suggestionId: string;
					word: string;
					startIndex: number;
					endIndex: number;
					suggestions: string[];
				}[] = [];

				// 可疑词汇
				if (transcript.suggestions && transcript.suggestions.length > 0) {
					transcript.suggestions.forEach((suggestion) => {
						const index = text.indexOf(suggestion.originalWord);
						if (index !== -1) {
							words.push({
								suggestionId: suggestion.suggestionId,
								word: suggestion.originalWord,
								startIndex: suggestion.startIndex,
								endIndex: suggestion.endIndex,
								suggestions: suggestion.suggestedWords,
							});
						}
					});
				}

				if (words.length > 0) {
					mockSuspiciousWords[transcript.id] = words;
				}
			});

			setSuspiciousWords(mockSuspiciousWords);
		}
	}, [listenInfo]);

	// 在handleAdd函数后添加新的文件上传处理函数
	const handleFileUpload = async (files: FileList) => {
		const fileArray = Array.from(files);
		console.log("fileArray", fileArray);
		// 使用函数式更新获取最新的材料列表
		let updatedMaterials: ListeningMaterial[] =
			listenInfo.listening?.material || [];
		// 使用队列机制逐个处理文件
		for (let i = 0; i < fileArray.length; i++) {
			const file = fileArray[i];
			// 检查文件是否存在
			if (!file) {
				continue;
			}

			// 验证文件类型
			if (!file.type.startsWith("image/")) {
				Toast.show({
					content: `${file.name} 不是有效的图片文件`,
					icon: "error",
				});
				continue;
			}

			// 验证文件大小 (例如限制为50MB)
			const maxSize = 50 * 1024 * 1024; // 50MB
			if (file.size > maxSize) {
				Toast.show({
					content: `${file.name} 文件过大，请选择小于50MB的图片`,
					icon: "error",
				});
				continue;
			}

			const fileId = `${Date.now()}-${Math.random().toString(36).substring(2, 10)}-${file.name}`;

			// 设置上传状态
			setUploadingFiles((prev) => ({
				...prev,
				[fileId]: { progress: 0, uploading: true },
			}));

			try {
				// 生成上传路径
				const key = generateKey(file.name, "listening/images");
				// 上传文件
				const uploadedUrl = await uploadObs(
					file,
					key,
					false,
					(progress: number) => {
						// 更新上传进度
						setUploadingFiles((prev) => ({
							...prev,
							[fileId]: { progress, uploading: true },
						}));
					},
				);

				// 上传完成，移除上传状态
				setUploadingFiles((prev) => {
					const newState = { ...prev };
					delete newState[fileId];
					return newState;
				});

				console.log("图片上传成功:", uploadedUrl);
				// 先调用API更新材料列表，再更新本地状态
				try {
					updatedMaterials = [
						...updatedMaterials,
						{ name: file.name, url: uploadedUrl, type: 1 },
					];
					// 等待API调用完成
					await updateMaterials({
						listeningId,
						materials: updatedMaterials,
					});

					// API成功后更新本地状态
					setListenInfo((prevListenInfo) => ({
						...prevListenInfo,
						listening: {
							...prevListenInfo.listening,
							material: updatedMaterials,
						},
					}));

					Toast.show({ content: `${file.name} 上传成功`, icon: "success" });
				} catch (apiError) {
					console.error("更新材料列表失败:", apiError);
					Toast.show({
						content: `${file.name} 上传成功，但更新列表失败`,
					});
				}
			} catch (error) {
				console.error("图片上传失败:", error);
				setUploadingFiles((prev) => {
					const newState = { ...prev };
					delete newState[fileId];
					return newState;
				});
				Toast.show({ content: `${file.name} 上传失败`, icon: "error" });
			}
		}
	};
	const handleDeleteFiles = async (item: ListeningMaterial, _index: number) => {
		await updateMaterials({
			listeningId,
			materials:
				listenInfo?.listening?.material?.filter((m) => m.url !== item.url) ||
				[],
		})
			.then(() => {
				Toast.show({ content: "删除成功" });
				// 更新本地状态
				setListenInfo((prevListenInfo) => ({
					...prevListenInfo,
					listening: {
						...prevListenInfo.listening,
						material:
							listenInfo?.listening?.material?.filter(
								(m) => m.url !== item.url,
							) || [],
					},
				}));
			})
			.catch(() => {
				Toast.show({ content: "删除失败" });
			});
	};
	const updateSummaryContents = async (newContent: string) => {
		await updateListeningContent({
			listeningId: listenInfo.listening?.listeningId,
			summaryContent: newContent,
		}).catch(() => {
			Toast.show({ content: "更新失败" });
		});
	};

	const updateTodos = async (newContent: string) => {
		await updateListeningSupportMeasure({
			listeningId: listenInfo.listening?.listeningId,
			summaryContent: newContent,
		}).catch(() => {
			Toast.show({ content: "更新失败" });
		});
	};

	const updateInterpretationText = async (newContent: string) => {
		await updateListeningInterpretation({
			listeningId: listenInfo.listening?.listeningId,
			summaryContent: newContent,
		}).catch(() => {
			Toast.show({ content: "更新失败" });
		});
	};

	// 添加标签处理函数
	const handleAddTag = async (newTagName: string) => {
		if (!newTagName.trim()) {
			Toast.show({ content: "标签名称不能为空" });
			return;
		}

		// 检查标签是否已存在
		const existingTag = listenInfo.tags?.find(
			(tag) => tag.name === newTagName.trim(),
		);
		if (existingTag) {
			Toast.show({ content: "标签已存在" });
			return;
		}

		await addTag({
			listeningId: listenInfo.listening?.listeningId || "",
			tagNames: [newTagName.trim()],
		})
			.then((res: any) => {
				console.log("res", res);
				Toast.show({ content: "标签添加成功", icon: "success" });
				setTagModalVisible(false);
				// 更新标签列表
				setListenInfo({
					...listenInfo,
					tags: res.tags,
				});
			})
			.catch(() => {
				Toast.show({ content: "标签添加失败", icon: "error" });
			});
	};

	// 删除标签处理函数
	const handleRemoveTag = async (tagId: string) => {
		Dialog.confirm({
			title: "确认删除",
			content: "确定要删除该标签吗？",
			onConfirm: async () => {
				await deleteTag({
					listeningId: listenInfo.listening?.listeningId || "",
					tagIds: [tagId],
				})
					.then(() => {
						setListenInfo({
							...listenInfo,
							tags: listenInfo.tags?.filter((tag) => tag.tagId !== tagId) || [],
						});
						Toast.show({ content: "标签删除成功" });
					})
					.catch(() => {
						Toast.show({ content: "标签删除失败" });
					});
			},
		});
	};

	useEffect(() => {
		console.log("params: ", { listeningId });
		if (listeningId) {
			setIsDataLoading(true);
			getListeningDetail({ listeningId })
				.then((res: any) => {
					console.log("res: ", res);
					setListenInfo(res);
				})
				.catch((error) => {
					console.error("获取详情失败:", error);
					Toast.show("获取详情失败，请重试");
				})
				.finally(() => {
					setIsDataLoading(false);
				});
		}
	}, [listeningId]);

	const handleExport = (data: ExportOptions) => {
		console.log("data", data);
		setIsExporting(true);
		exportListeningRecode({
			listeningId,
			type: data.format === "audio" ? 1 : 2,
			showSpeaker: data.includeTranscript,
			showTimestamp: data.includeParticipants,
			format: data.fileFormat,
		})
			.then((res: any) => {
				console.log("res: ", res);
				setExportResult({
					fileName: res.fileName,
					fileUrl: res.fileUrl,
					visible: true,
				});
			})
			.finally(() => {
				setIsExporting(false);
			});
	};

	const handleDownload = () => {
		console.log("handleDownload", exportResult);
		const link = document.createElement("a");
		link.href = exportResult.fileUrl;
		link.download = exportResult.fileName;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		setExportResult((prev) => ({ ...prev, visible: false }));
	};

	const closeExportModal = () => {
		setExportResult((prev) => ({ ...prev, visible: false }));
	};

	const jumpAudio = (item: any) => {
		// 毫秒转成秒
		const targetSeconds = item.startOffset / 1000;
		setPositionSeconds(targetSeconds);
		console.log("跳转到时间:", targetSeconds, "秒");
	};

	const handleIgnore = () => {
		const { transcriptId, wordIndex, suggestionId } = replacementModal;

		handleSuggestion({
			listeningId,
			segmentId: transcriptId,
			suggestionId,
			action: "ignore",
		}).then(() => {
			const updatedSuspiciousWords = { ...suspiciousWords };
			if (updatedSuspiciousWords[transcriptId]) {
				// 相关词汇建议
				updatedSuspiciousWords[transcriptId] = updatedSuspiciousWords[
					transcriptId
				].filter((_, index) => index !== wordIndex);
				setSuspiciousWords(updatedSuspiciousWords);
			}
			setReplacementModal({ ...replacementModal, visible: false });
		});
	};

	// 处理词汇替换
	const handleWordReplacement = (newWord: string) => {
		const { transcriptId, wordIndex, suggestionId } = replacementModal;
		const wordInfo = suspiciousWords[transcriptId]?.[wordIndex];

		if (!wordInfo) return;

		// 更新转录文本
		handleSuggestion({
			listeningId,
			segmentId: transcriptId,
			suggestionId,
			action: "accept",
			acceptedWord: newWord,
		}).then((response: any) => {
			// 使用接口返回的数据更新转录文本
			const updatedTranscripts = listenInfo.segments?.map((transcript: any) => {
				if (transcript.id === transcriptId) {
					return {
						...transcript,
						transcriptText: response.transcriptText, // 使用接口返回的文本
						suggestions: response.suggestions, // 使用接口返回的建议列表
					};
				}
				return transcript;
			});

			setListenInfo({
				...listenInfo,
				segments: updatedTranscripts,
			});

			// 使用接口返回的建议数据更新可疑词汇列表
			const updatedSuspiciousWords = { ...suspiciousWords };

			if (response.suggestions && response.suggestions.length > 0) {
				// 根据接口返回的建议重新构建可疑词汇列表
				const newSuspiciousWords = response.suggestions.map(
					(suggestion: any) => ({
						suggestionId: suggestion.suggestionId,
						word: suggestion.originalWord,
						startIndex: suggestion.startIndex,
						endIndex: suggestion.endIndex,
						suggestions: suggestion.suggestedWords,
					}),
				);
				updatedSuspiciousWords[transcriptId] = newSuspiciousWords;
			} else {
				// 如果没有建议了，删除整个记录
				delete updatedSuspiciousWords[transcriptId];
			}

			setSuspiciousWords(updatedSuspiciousWords);
			setReplacementModal({ ...replacementModal, visible: false });
		});
	};

	const closeReplacementModal = () => {
		setReplacementModal({ ...replacementModal, visible: false });
	};
	// 处理 TranscriptList 中可疑词点击回调，打开替换弹窗
	const handleWordClick = ({
		transcriptId,
		wordIndex,
		wordInfo,
		anchor,
	}: {
		transcriptId: string;
		wordIndex: number;
		wordInfo: {
			suggestionId: string;
			word: string;
			startIndex: number;
			endIndex: number;
			suggestions: string[];
		};
		anchor: DOMRect;
	}) => {
		setReplacementModal({
			visible: true,
			transcriptId,
			wordIndex,
			word: wordInfo.word,
			suggestionId: wordInfo.suggestionId,
			suggestions: wordInfo.suggestions,
			position: { x: anchor.left + anchor.width / 2, y: anchor.top - 10 },
		});
	};

	// 修改toggleSpeakerTextEditing函数，改为打开编辑sheet
	const openTranscriptEditSheet = (id: string) => {
		const transcript = listenInfo.segments?.find((item) => item.id === id);
		setTranscriptEditSheet({
			visible: true,
			transcript: transcript || null,
			id,
		});
	};

	// 处理编辑sheet的保存
	const handleTranscriptSave = async (data: {
		speakerRole: number;
		speakerLabel: string;
		speakerId: string;
		transcriptText: string;
		globalUpdate: boolean;
	}) => {
		const { id } = transcriptEditSheet;
		if (id === "") return;

		const transcript = listenInfo.segments?.find((item) => item.id === id);
		const originalSpeakerLabel = transcript?.speakerLabel ?? "";
		const originalSpeakerId = transcript?.speakerId ?? "";
		const originalSpeakerRole = transcript?.speakerRole ?? 0;
		const originalTranscriptText = transcript?.transcriptText ?? "";
		const { globalUpdate } = data;

		setIsTranscriptSaving(true);

		try {
			// 准备API调用数组
			const apiCalls = [];

			// 更新转录文本内容
			if (originalTranscriptText !== data.transcriptText) {
				apiCalls.push(
					updateListeningRecodeTranscriptSegments({
						listeningId: listenInfo.listening?.listeningId,
						segmentId: transcript?.id || "",
						transcriptText: data.transcriptText,
					}),
				);
			}

			// 更新发言人标签（如果发生变化）
			if (
				originalSpeakerLabel !== data.speakerLabel ||
				originalSpeakerId !== data.speakerId ||
				originalSpeakerRole !== data.speakerRole
			) {
				if (!globalUpdate) {
					apiCalls.push(
						updateListenRecodeTranscriptSpeaker({
							listeningId: listenInfo.listening?.listeningId || "",
							segmentId: transcript?.id || "",
							newSpeakerId: data.speakerId,
							newSpeakerRole: data.speakerRole,
						}),
					);
				} else {
					if (originalSpeakerRole === 0) {
						apiCalls.push(
							updateListenRecodeTranscriptAllSpeaker({
								listeningId: listenInfo.listening?.listeningId || "",
								originalSpeakerLabel,
								newSpeakerId: data.speakerId,
								newSpeakerRole: data.speakerRole,
							}),
						);
					} else {
						apiCalls.push(
							updateListenRecodeTranscriptAllSpeaker({
								listeningId: listenInfo.listening?.listeningId || "",
								originalSpeaker: {
									speakerId: originalSpeakerId,
									speakerRole: originalSpeakerRole,
								},
								newSpeakerId: data.speakerId,
								newSpeakerRole: data.speakerRole,
							}),
						);
					}
				}
			}

			// 使用Promise.all确保所有API调用都完成
			await Promise.all(apiCalls);

			// 同步更新listenInfo中的segments数据
			if (
				originalSpeakerLabel !== data.speakerLabel ||
				originalSpeakerId !== data.speakerId ||
				originalSpeakerRole !== data.speakerRole ||
				originalTranscriptText !== data.transcriptText
			) {
				const updatedListenInfo = {
					...listenInfo,
					segments: listenInfo.segments?.map((segment) => {
						if (segment.id === transcript?.id) {
							if (originalTranscriptText !== data.transcriptText) {
								return {
									...segment,
									suggestions: [],
									transcriptText: data.transcriptText,
									speakerLabel: data.speakerLabel,
									speakerId: data.speakerId,
									speakerRole: data.speakerRole,
								};
							}
							return {
								...segment,
								transcriptText: data.transcriptText,
								speakerLabel: data.speakerLabel,
								speakerId: data.speakerId,
								speakerRole: data.speakerRole,
							};
						}
						if (globalUpdate) {
							// 全局更新时，更新所有匹配的发言人
							if (
								segment.speakerLabel === originalSpeakerLabel &&
								segment.speakerId === originalSpeakerId
							) {
								return {
									...segment,
									speakerLabel: data.speakerLabel,
									speakerId: data.speakerId,
									speakerRole: data.speakerRole,
								};
							}
						}
						return segment;
					}),
				};
				setListenInfo(updatedListenInfo);
			}

			// 关闭编辑sheet
			setTranscriptEditSheet({ visible: false, transcript: null, id: "" });
			Toast.show({ content: "保存成功", icon: "success" });
		} catch (error) {
			console.error("更新转录记录失败:", error);
			Toast.show({ content: "保存失败，请重试" });
		} finally {
			setIsTranscriptSaving(false);
		}
	};

	const handlePreview = (item: ListeningMaterial) => {
		console.log("handlePreview", item);
		setImagePreview({
			visible: true,
			imageUrl: item.url || "",
			imageName: item.name || "图片",
		});
	};

	const handleRegenerate = async () => {
		if (!listenInfo.listening?.listeningId) {
			return;
		}
		if (listenInfo.listening.status === 6) {
			// 检查所有发言人的speakerRole是否都不为0
			const allSpeakersIdentified = listenInfo.segments?.every(
				(segment) => segment.speakerRole !== 0,
			);
			if (!allSpeakersIdentified) {
				Toast.show({
					content: "请先指认所有发言人",
				});
				return;
			}
			try {
				await reGenerate({
					listeningId: listenInfo.listening?.listeningId,
				});
				Toast.show({ content: "生成中，请耐心等待" });
				setTimeout(() => {
					onBackPressed();
				}, 1000);
			} catch (error) {
				console.error("生成失败:", error);
				Toast.show({ content: "生成失败，请重试" });
			}
			return;
		}
		Dialog.confirm({
			title: "",
			content: "确认重新生成吗？",
			onConfirm: async () => {
				try {
					await reGenerate({
						listeningId: listenInfo.listening?.listeningId,
					});
					Toast.show({ content: "重新生成中，请耐心等待" });
					setTimeout(() => {
						onBackPressed();
					}, 1000);
				} catch (error) {
					console.error("重新生成失败:", error);
					Toast.show({ content: "重新生成失败，请重试" });
				}
			},
		});
	};

	const closeImagePreview = () => {
		setImagePreview({ ...imagePreview, visible: false });
	};

	if (isDataLoading) {
		return <LoadingScreen title="倾听详情" loadingText="加载中..." />;
	}

	return (
		<div
			className="flex flex-col bg-gray-50 overscroll-contain overflow-hidden"
			style={{ height: "var(--app-height, 100dvh)" }}
		>
			<TopBar
				title="倾听详情"
				onBack={onBackPressed}
				onExport={() => setExportFileVisible(true)}
				onRegenerate={handleRegenerate}
				canRegenerate={listenInfo.listening?.staffInfo?.roleId === userId}
				status={listenInfo.listening?.status}
			/>
			<div className="flex-1 overflow-y-auto pt-14 pb-[var(--audio-player-height,160px)]">
				<div className="p-4">
					{/* 内容区域（InfoCard / Sections / TranscriptList ...） */}
					<InfoCard
						listenInfo={listenInfo}
						onAddTagClick={() => setTagModalVisible(true)}
						onRemoveTag={handleRemoveTag}
						canEdit={userId === listenInfo.listening?.staffInfo?.roleId}
					/>
					{/* 小组倾听待指认提示条 */}
					<GroupListeningAlert
						type={listenInfo.listening?.type}
						status={listenInfo.listening?.status}
					/>
					<ContextDescriptionSection
						contextDescription={listenInfo.listening?.contextDescription}
						canEdit={userId === listenInfo.listening?.staffInfo?.roleId}
						onEdit={() => setIsContextDescriptionEditing(true)}
					/>

					<ObservationSection
						summaryContents={listenInfo.summaryContents}
						canEdit={userId === listenInfo.listening?.staffInfo?.roleId}
						onEdit={() => setIsSummaryContentsEditing(true)}
					/>

					<NarrativeRadar
						type={listenInfo.listening?.studentInfos?.length === 1 ? 1 : 2}
						canEdit={userId === listenInfo.listening?.staffInfo?.roleId}
						onEdit={() => setIsInterpretationTextEditng(true)}
						metrics={listenInfo.narrativeAnalysis?.metrics}
						interpretationText={listenInfo.interpretationText}
					/>

					<SupportSection
						todos={listenInfo.todos}
						canEdit={userId === listenInfo.listening?.staffInfo?.roleId}
						onEdit={() => setIsTodosEditing(true)}
					/>
					<MaterialsSection
						materials={listenInfo.listening?.material}
						onPreview={handlePreview}
						onFileUpload={handleFileUpload}
						uploadingFiles={uploadingFiles}
						onDelete={handleDeleteFiles}
						canUpload={userId === listenInfo.listening?.staffInfo?.roleId}
					/>

					<TranscriptList
						transcripts={listenInfo.segments || []}
						canEdit={userId === listenInfo.listening?.staffInfo?.roleId}
						onToggleSpeakerTextEditing={openTranscriptEditSheet} // 修改这里
						onJumpAudio={jumpAudio}
						suspiciousWords={suspiciousWords}
						onWordClick={handleWordClick}
					/>
					{listenInfo.recording?.fileUrl && (
						<AudioPlayer
							audioUrl={listenInfo.recording?.fileUrl}
							onSeek={(position) => setPositionSeconds(position / 1000)}
							positionSeconds={positionSeconds}
						/>
					)}
				</div>
			</div>

			<ExportFile
				listeningId={listeningId}
				onClose={() => setExportFileVisible(false)}
				onExport={handleExport}
				visible={exportFileVisible}
			/>

			{/* 导出加载状态 */}
			{isExporting && <Loading />}

			{/* 导出成功弹窗 */}
			<ExportSuccessModal
				visible={exportResult.visible}
				fileName={exportResult.fileName}
				fileUrl={exportResult.fileUrl}
				onClose={closeExportModal}
				onDownload={handleDownload}
			/>
			{/* 词汇替换弹窗 */}
			<WordReplacementModal
				visible={replacementModal.visible}
				word={replacementModal.word}
				suggestions={replacementModal.suggestions}
				onClose={closeReplacementModal}
				onWordReplace={handleWordReplacement}
				onIgnore={handleIgnore}
			/>

			{/* 图片预览模态框 - 使用新组件 */}
			<ImagePreviewModal
				visible={imagePreview.visible}
				imageUrl={imagePreview.imageUrl}
				imageName={imagePreview.imageName}
				onClose={closeImagePreview}
			/>
			{/* 编辑记录内容对话框 */}
			<EditText
				emptyMessage="教师观察与解读不能为空"
				initialContent={listenInfo.summaryContents ?? ""}
				onClose={() => setIsSummaryContentsEditing(false)}
				onSave={async (content) => {
					await updateSummaryContents(content);
				}}
				onSuccess={(newContent) => {
					setListenInfo({
						...listenInfo,
						summaryContents: newContent,
					});
				}}
				placeholder="请输入教师观察与解读"
				title="编辑教师观察与解读"
				visible={isSummaryContentsEditing}
			/>

			{/* 编辑支持内容 */}
			<EditText
				emptyMessage="支持与措施不能为空"
				initialContent={listenInfo.todos ?? ""}
				onClose={() => setIsTodosEditing(false)}
				onSave={async (content) => {
					await updateTodos(content);
				}}
				onSuccess={(newContent) => {
					setListenInfo({
						...listenInfo,
						todos: newContent,
					});
				}}
				placeholder="请输入支持与措施"
				title="编辑支持与措施"
				visible={isTodosEditing}
			/>
			{/* 编辑情景说明 */}
			<EditText
				emptyMessage="情景说明不能为空"
				maxLength={100}
				initialContent={listenInfo.listening?.contextDescription ?? ""}
				onClose={() => setIsContextDescriptionEditing(false)}
				onSave={async (content) => {
					await updateListeningContextDescription({
						listeningId: listenInfo.listening?.listeningId,
						summaryContent: content,
					});
				}}
				onSuccess={(newContent) => {
					setListenInfo({
						...listenInfo,
						listening: {
							...listenInfo.listening,
							contextDescription: newContent,
						},
					});
				}}
				placeholder="请输入情景说明"
				title="编辑情景说明"
				visible={isContextDescriptionEditing}
			/>
			{/* 编辑分析与解读 */}
			<EditText
				emptyMessage="综合分析解读不能为空"
				initialContent={listenInfo.interpretationText ?? ""}
				onClose={() => setIsInterpretationTextEditng(false)}
				onSave={async (content) => {
					await updateInterpretationText(content);
				}}
				onSuccess={(newContent) => {
					setListenInfo({
						...listenInfo,
						interpretationText: newContent,
					});
				}}
				placeholder="请输入综合分析解读"
				title="编辑综合分析解读"
				visible={isInterpretationTextEditng}
			/>
			{/* 标签输入弹窗 */}
			<TagInputModal
				visible={tagModalVisible}
				onClose={() => setTagModalVisible(false)}
				onConfirm={handleAddTag}
				existingTags={listenInfo.tags}
			/>
			{/* 转录内容编辑Sheet */}
			<TranscriptEditSheet
				initialGlobalUpdate={false}
				visible={transcriptEditSheet.visible}
				onClose={() =>
					setTranscriptEditSheet({
						visible: false,
						transcript: null,
						id: "",
					})
				}
				transcript={transcriptEditSheet.transcript}
				listenInfo={listenInfo}
				onSave={handleTranscriptSave}
				loading={isTranscriptSaving}
			/>
		</div>
	);
};

export default DetailScreen;
