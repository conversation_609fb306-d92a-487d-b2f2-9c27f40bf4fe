/** biome-ignore-all lint/suspicious/noExplicitAny: <explanation> */
'use client';

import { DotLoading, ImageViewer, Swiper } from 'antd-mobile';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';

import { getMonitorReportInfo } from '@/api/member';
import { hinaTrack } from '@/utils';

const DailyReport = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const summaryId = searchParams.get('summaryId');
  const [timelineData, setTimelineData] = useState({
    items: [],
    summaryDate: '',
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    console.log(`summaryId=${summaryId}`);
    setIsLoading(true);
    getMonitorReportInfo(summaryId ?? '')
      .then((res: any) => {
        console.log(`detail: ${JSON.stringify(res)}`);
        setTimelineData(res);
      })
      .finally(() => {
        setIsLoading(false);
      });
    hinaTrack('videoreport_details_pageview');
  }, []);

  // 返回按钮点击事件
  const handleBackClick = () => {
    router.back(); // 使用 Next.js router 返回上一页
  };

  return (
    <div
      className="relative flex min-h-screen w-full flex-col font-sans"
      style={{
        backgroundImage: "url('/images/live/bg.png')",
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        backgroundPosition: 'top',
      }}
    >
      <header
        className="fixed inset-x-0 top-0 z-20 w-full"
        style={{
          backgroundImage: "url('/images/live/bg.png')",
          backgroundRepeat: 'no-repeat',
          backgroundSize: '100% auto',
          backgroundPosition: 'top center',
        }}
      >
        <div className="relative flex w-full items-center justify-center">
          <div className="inset-0 mt-14 mb-2 flex items-center justify-center">
            <button
              className="absolute left-4 flex items-center justify-center text-gray-800"
              onClick={handleBackClick}
            >
              <img
                alt="返回"
                className="size-6 object-contain"
                src="/images/live/arrow_left.png"
              />
            </button>
            <h1 className="text-center font-bold text-black text-xl">
              视频日报·精彩瞬间
            </h1>
          </div>
        </div>
      </header>

      {isLoading ? (
        <div className="flex h-screen flex-col items-center justify-center">
          <span style={{ fontSize: 16 }}>
            <span className="text-gray-500">加载中</span>
            <DotLoading />
          </span>
        </div>
      ) : timelineData.items.length === 0 ? (
        <div className="flex h-screen flex-col items-center justify-center">
          <p className="text-gray-500">暂无数据</p>
        </div>
      ) : (
        <div className="relative w-full grow overflow-y-auto pt-24">
          <div
            className="absolute top-20 bottom-0 left-[44px] w-0.5 border-amber-300" // 移除 border-dashed，因为我们要用背景图模拟
            style={{
              // 使用线性渐变模拟虚线
              // background: 'repeating-linear-gradient(to bottom, #FCD34D 0px, #FCD34D 10px, transparent 10px, transparent 20px)',
              // border-l-2 的颜色通常是 border-amber-300
              // 假设 #FCD34D 是 amber-300 的一个近似色
              background:
                'repeating-linear-gradient(to bottom, #FCD34D 0, #FCD34D 5px, transparent 5px, transparent 10px)',
              backgroundRepeat: 'repeat-y', // 垂直重复
              backgroundSize: '100% 10px', // 每隔 10px 重复一次模式，其中 5px 是线，5px 是间距
            }}
          />
          {/* 阳光图标 */}
          <div className="absolute top-16 right-[25px] z-30 size-20 bg-[url('/images/live/sun.png')] bg-contain bg-no-repeat" />

          {/* 主体内容 */}
          <main className="px-4">
            <div className="relative mt-2">
              <div className="mb-4 pb-8">
                <div className="space-y-4">
                  {timelineData.items.map((item: any) => (
                    <TimelineCard item={item} key={item.id} />
                  ))}
                </div>

                {timelineData.items && (
                  <p className="mt-4 ml-2 text-center text-gray-400 text-xs">
                    本内容为AI生成，仅供参考不代表平台立场
                  </p>
                )}
              </div>
            </div>
          </main>
        </div>
      )}
    </div>
  );
};

const TimelineCard = ({ item }: { item: any }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const textRef = useRef<HTMLParagraphElement>(null);
  const [showExpandButton, setShowExpandButton] = useState(false);

  useEffect(() => {
    // 只有当文本框存在时才进行测量
    if (textRef.current) {
      // 临时移除 line-clamp-2 样式，获取真实内容高度
      textRef.current.style.webkitLineClamp = 'unset'; // 移除 line-clamp 限制
      textRef.current.style.display = 'block'; // 确保它是块级元素以便正确测量

      const fullHeight = textRef.current.scrollHeight; // 获取完整内容高度

      // 重新应用 line-clamp-2，以获取限制后的高度
      // 只有当 isExpanded 为 false 时才重新应用 line-clamp-2
      if (!isExpanded) {
        textRef.current.style.webkitLineClamp = '2'; // 重新设置回 2 行
        textRef.current.style.display = '-webkit-box'; // 重新设置回 webkit-box
      }

      const clampedHeight = textRef.current.clientHeight; // 获取两行限制后的高度

      // 如果完整高度大于两行限制后的高度，则显示“展开”按钮
      // 注意：这里的判断应该只在 isExpanded 为 false 的情况下才考虑显示展开按钮
      // 如果 isExpanded 已经是 true，那么无论是否溢出，都不应该显示“展开”按钮，而是“收起”按钮
      // 所以我们直接判断 fullHeight > clampedHeight + 1，并将这个结果用于条件渲染按钮
      setShowExpandButton(fullHeight > clampedHeight + 1);
    }
  }, [item.summary]); // 只需要在 summaryText 变化时重新计算是否需要显示按钮

  const renderFooterText = (_image: string, index: number) => {
    return (
      <div className="mb-4 flex justify-center text-1xs text-white">
        <span>{`${index + 1} / ${item.images.length}`}</span>
      </div>
    );
  };

  return (
    <div className="relative flex flex-col">
      <style>{`
        .adm-image-viewer-indicator,
        .adm-image-viewer-header {
          display: none !important;
        }
      `}</style>
      {/* 第一行：圆点 和 日期、星期 (水平对齐) */}
      <div className="flex items-center">
        <div className="-ml-4 relative flex size-8 shrink-0 items-center justify-center">
          <span className="-translate-x-1/2 -translate-y-1/2 absolute top-1/2 left-6 size-3 rounded-full border-2 border-white bg-yellow-500" />
        </div>

        <div className="ml-2 flex">
          <p className="font-semibold text-[rgb(253,145,70,1)] text-base leading-tight">
            {item.time}
          </p>
          <p className="ml-2 text-gray-500 text-xs leading-tight">
            {item.dayOfWeek}
          </p>
        </div>
      </div>

      {/* 第二行：卡片 (在日期/星期下方，并左对齐) */}
      <div className="mt-2 ml-5">
        <div className="overflow-hidden rounded-xl bg-white shadow-md transition-transform duration-300">
          <div className="h-40 w-full object-cover">
            <Swiper
              autoplay
              autoplayInterval={3000}
              direction="horizontal"
              loop
            >
              {item.images.map((url: string, index: number) => {
                return (
                  <Swiper.Item key={url}>
                    <div
                      onClick={() => {
                        ImageViewer.Multi.show({
                          images: item.images,
                          defaultIndex: index,
                          renderFooter: renderFooterText,
                        });
                      }}
                    >
                      <img
                        alt="img"
                        className="h-40 w-full object-cover"
                        src={url}
                      />
                    </div>
                  </Swiper.Item>
                );
              })}
            </Swiper>
          </div>
          <div className="p-2">
            {item.tags && item.tags.length > 0 && (
              <div className="mt-1 mb-2 flex flex-wrap gap-2 px-1">
                {item.tags.map((tag: string, index: number) => (
                  <span
                    className="rounded-full bg-yellow-300 px-2 py-0.5 font-medium text-black text-xs"
                    key={index}
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}

            <p
              className={`px-2 text-base text-gray-950 leading-relaxed ${isExpanded ? '' : 'line-clamp-2'}`}
              // 根据 isExpanded 动态切换样式
              ref={textRef}
            >
              {item.summary || '暂无内容'}
            </p>

            {showExpandButton && (
              <div className="my-2 flex justify-center">
                {' '}
                {/* 添加 flex justify-center 用于水平居中 */}
                <button
                  className="font-semibold text-base text-blue-500" // 点击后切换 isExpanded 状态
                  onClick={() => setIsExpanded(!isExpanded)}
                >
                  {isExpanded ? '收起' : '展开全部'}{' '}
                  {/* 根据 isExpanded 状态显示不同文本 */}
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DailyReport;
