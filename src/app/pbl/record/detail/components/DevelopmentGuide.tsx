import { Popup } from 'antd-mobile';
import clsx from 'clsx';
import { ChevronDown, Circle, CircleCheck, HelpCircle } from 'lucide-react';
import type React from 'react';
import { useEffect, useMemo, useState } from 'react';
import {
  Legend,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
} from 'recharts';
import { getObservationEvaluation } from '@/api/pbl';
import { formatEvaluationData } from '../../create/components/DevelopmentGuide';
import Icon, { type IconName } from '../../create/components/Icon';

// 单个目标项组件
function GoalItem({
  goal,
  isAchieved,
}: {
  goal: {
    id: string;
    ability_name: string;
    observationText?: string;
  };
  isAchieved: boolean;
}) {
  const [showPopup, setShowPopup] = useState(false);

  const handleQuestionClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowPopup(true);
  };

  return (
    <>
      <li
        className={clsx(
          'flex items-center space-x-2 py-2.5',
          !isAchieved && 'opacity-50' // 未达成的目标降低透明度
        )}
      >
        {isAchieved ? (
          <CircleCheck className="h-4 w-4 text-green-500" />
        ) : (
          <Circle className="h-4 w-4 text-gray-400" />
        )}

        <span
          className={clsx(
            'flex-1 text-sm',
            isAchieved ? 'text-gray-800' : 'text-gray-600'
          )}
        >
          {goal.ability_name}
        </span>

        {isAchieved && (
          <button
            className="rounded-full p-1 transition-colors hover:bg-gray-100"
            onClick={handleQuestionClick}
            type="button"
          >
            <HelpCircle className="h-4 w-4 text-gray-600 hover:text-gray-600" />
          </button>
        )}
      </li>

      {/* 观察文本弹窗 */}
      <Popup
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '30vh',
          padding: '20px',
        }}
        onMaskClick={() => setShowPopup(false)}
        visible={showPopup}
      >
        <div className="space-y-4">
          <div className="text-base text-gray-900">
            <span className="font-semibold">"{goal.ability_name}"</span>{' '}
            能力点关联观察内容
          </div>
          <div className="rounded-lg bg-gray-50 p-4">
            <div className="text-gray-700 leading-relaxed">
              {goal.observationText || '暂无相关观察内容'}
            </div>
          </div>

          <button
            className="w-full rounded-lg bg-indigo-500 py-3 font-medium text-white transition-all hover:bg-indigo-600 active:scale-95"
            onClick={() => setShowPopup(false)}
            type="button"
          >
            关闭
          </button>
        </div>
      </Popup>
    </>
  );
}

function SubCategorySection({ subCategory, achievedGoals }) {
  return (
    <div className="mb-5 last:mb-0">
      <h4 className="mb-2 font-semibold text-base text-gray-700">
        {subCategory.label}
      </h4>
      <ul className="m-0 list-none p-0">
        {subCategory.options?.map((goal) => (
          <GoalItem
            goal={goal}
            isAchieved={new Set(achievedGoals).has(Number(goal.id))}
            key={goal.id}
          />
        ))}
      </ul>
    </div>
  );
}

function CategoryAccordion({ category, achievedGoals, isOpen, onToggle }) {
  // 计算该主分类下的总达成数和总目标数
  const allGoals = category.subDomains.flatMap(
    (sc) => sc.options?.flatMap((o) => o.id) || []
  );
  const achievedCount = allGoals.filter((goal) =>
    new Set(achievedGoals).has(Number(goal))
  ).length;
  const totalCount = allGoals.length;

  return (
    <div className="mb-4 overflow-hidden rounded-lg bg-white transition-all duration-300 ease-in-out">
      <button
        className={clsx(
          'flex w-full items-center justify-between p-4 text-left transition-colors duration-200 focus:outline-none',
          category.bgColor // 使用分类的背景色
        )}
        onClick={onToggle}
        type="button"
      >
        <div className="flex items-center space-x-3">
          <Icon
            className={clsx('h-4 w-4', category.color)}
            name={category.icon}
          />
          <span className={clsx('text-base', category.color)}>
            {category.label}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <span className={clsx('font-medium text-sm', category.color)}>
            {`已达成 ${achievedCount}/${totalCount}`}
          </span>
          <ChevronDown
            className={clsx(
              'h-5 w-5 transform text-gray-500 transition-transform duration-300',
              isOpen ? 'rotate-180' : 'rotate-0'
            )}
          />
        </div>
      </button>

      <div
        className={clsx(
          'overflow-hidden border border-gray-100 border-t-0 transition-all duration-500 ease-in-out',
          isOpen ? 'max-h-[1200px] opacity-100' : 'max-h-0 opacity-0' // 使用 max-h 和 opacity 实现平滑过渡
        )}
      >
        <div className="p-4">
          {category.subDomains.map((subCategory) => (
            <SubCategorySection
              achievedGoals={achievedGoals}
              key={subCategory.label}
              subCategory={subCategory}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

function DevelopmentGuide({ evaluations }: { evaluations: string[] }) {
  console.log('🚀 ~ evaluations:', evaluations);
  const [evaluationData, setEvaluationData] = useState<any[]>([]);

  useEffect(() => {
    getObservationEvaluation({
      dimensionType: 2,
    }).then((res) => {
      //@ts-expect-error
      const data = res?.dimensions?.[0]?.domains || [];
      setEvaluationData(formatEvaluationData(data));
    });
  }, []);
  const [openCategoryId, setOpenCategoryId] = useState<number | null>(null); // 默认展开第一个

  const handleToggle = (categoryId: number | null) => {
    setOpenCategoryId((prevId: number | null) =>
      prevId === categoryId ? null : categoryId
    );
  };

  // 计算雷达图数据
  const radarData = useMemo(() => {
    if (!evaluationData.length) {
      return [];
    }

    const result = evaluationData.map((category: any) => {
      // 计算该分类下的总目标数和已达成数 - 使用与CategoryAccordion相同的逻辑
      const allGoals = category.subDomains.flatMap(
        (sc: any) => sc.options?.flatMap((o: any) => o.id) || []
      );

      const achievedCount = allGoals.filter((goal: any) =>
        new Set(evaluations.map(Number)).has(Number(goal))
      ).length;

      const totalCount = allGoals.length;
      const percentage =
        totalCount > 0 ? Math.round((achievedCount / totalCount) * 100) : 0;

      console.log(
        `🎯 ${category.label}: ${achievedCount}/${totalCount} = ${percentage}%`
      );

      return {
        subject: category.label,
        value: percentage,
        fullMark: 100,
      };
    });

    console.log('📊 雷达图数据:', result);
    return result;
  }, [evaluationData, evaluations]);

  return (
    <div className="custom-scrollbar flex-grow overflow-y-auto">
      {/* 雷达图 */}
      {radarData.length > 0 && (
        <div className="mb-6 rounded-lg border border-gray-100 bg-white p-4 shadow-sm">
          <div className="mb-4 border-gray-100 border-b pb-2">
            <span className="font-medium text-gray-600">
              3-6岁儿童学习与发展指南 - 能力达成情况
            </span>
          </div>
          <div style={{ height: '300px', width: '100%' }}>
            <ResponsiveContainer height="100%" width="100%">
              <RadarChart data={radarData} outerRadius="80%">
                <PolarGrid stroke="#e0e0e0" />
                <PolarAngleAxis
                  dataKey="subject"
                  tick={{ fill: '#666', fontSize: 14 }}
                />
                <PolarRadiusAxis
                  angle={18}
                  domain={[0, 100]}
                  tick={{ fill: '#999', fontSize: 10 }}
                />
                <Radar
                  dataKey="value"
                  fill="#6366f1"
                  fillOpacity={0.6}
                  name="达成率(%)"
                  stroke="#6366f1"
                />
                <Legend
                  wrapperStyle={{ fontSize: '12px', paddingTop: '10px' }}
                />
              </RadarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {evaluationData.map((category) => (
        <CategoryAccordion
          achievedGoals={evaluations}
          category={category}
          isOpen={openCategoryId === category.id}
          key={category.id}
          onToggle={() => handleToggle(category.id)}
        />
      ))}
    </div>
  );
}

export default DevelopmentGuide;
