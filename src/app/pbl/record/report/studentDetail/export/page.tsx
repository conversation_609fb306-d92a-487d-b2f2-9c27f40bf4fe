'use client';

import { DotLoading, Grid } from 'antd-mobile';
import { ArrowDownCircle, ArrowUpCircle } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import {
  Legend,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
} from 'recharts';

import { getObservationDimensions, getStudentReportDetail } from '@/api/pbl';

export default function GenerateReportPage() {
  if (typeof window !== 'undefined') {
    document.title = '报告详情';
  }
  const searchParams = useSearchParams();
  const reportId = searchParams.get('reportId');
  const sourceType = searchParams.get('sourceType');
  const [loading, setLoading] = useState(false);
  const [studentName, setStudentName] = useState('');
  const [ability] = useState<any>({
    communication: '沟通能力',
    collaboration: '协作能力',
    creativity: '创造力',
    criticalThinking: '批判性思维',
    citizenshipCharacterEducation: '公民意识与品格',
  });
  const [fieldRadarChart, setFieldRadarChart] = useState([
    { subject: '健康', score: 1, fullMark: 100, field: 'healthScore' },
    { subject: '语言', score: 2, fullMark: 100, field: 'languageScore' },
    { subject: '社会', score: 4, fullMark: 100, field: 'societyScore' },
    { subject: '科学', score: 3, fullMark: 100, field: 'scienceScore' },
    { subject: '艺术', score: 2, fullMark: 100, field: 'artsScore' },
  ]);
  const [dimensions, setDimensions] = useState<string[]>([]);

  const [dimensionsList] = useState([
    {
      dimensions: '2',
      label: '1. 《3-6岁儿童学习与发展指南》',
      value: '评估指标包括：5个领域，12个子领域，共36项评估指标。',
    },
    {
      dimensions: '3',
      label: '2. 《幸福感和参与度指标》',
      value: '评估指标包括：2个领域，10个子领域，共10项评估指标。',
    },
    {
      dimensions: '1',
      label: '3. 《深度学习能力指标》',
      value: '评估指标包括：5个领域，12个子领域，共36项评估指标。',
    },
  ]);
  const [abilityRadar, setAbilityRadar] = useState([
    {
      subject: '健康 ',
      current: 92,
      last: 90,
      fullMark: 100,
      field: 'healthPct',
    },

    {
      subject: '语言',
      current: 80,
      last: 90,
      fullMark: 100,
      field: 'languageFullPct',
    },
    {
      subject: '社会',
      current: 64,
      last: 85,
      fullMark: 100,
      field: 'societyPct',
    },
    {
      subject: '科学',
      current: 83,
      last: 95,
      fullMark: 100,
      field: 'sciencePct',
    },

    {
      subject: '艺术',
      current: 72,
      last: 75,
      fullMark: 100,
      field: 'artsFullPct',
    },
  ]);
  const [reportData, setReportData] = useState<any>({
    deepLearningCounters: {},
  });

  useEffect(() => {
    setLoading(true);
    const fetchReportData = async () => {
      try {
        const response = (await getStudentReportDetail({
          reportId,
          sourceType,
        })) as any;
        setStudentName(response.studentReport.studentName);
        const fieldRadarChartTemp = fieldRadarChart.map((item) => {
          return {
            ...item,
            score: response.radarChart[item.field],
          };
        });
        const abilityRadarChartTemp = abilityRadar.map((item) => {
          return {
            ...item,
            current: response.radarChart[item.field],
            last: response.prefRadarChart?.[item.field] ?? 0,
          };
        });
        setFieldRadarChart(fieldRadarChartTemp);
        setAbilityRadar(abilityRadarChartTemp);
        setReportData(response);
        setLoading(false);
      } catch (error) {
        setLoading(false);
      }
    };
    getObservationDimensions({ sourceType }).then((res: any) => {
      setDimensions(res.dimensions);
    });
    if (reportId) {
      fetchReportData();
    }
  }, [reportId]);

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4">
        <DotLoading color="primary" />
        <span className="mt-2 ml-2 text-gray-500">正在打开评价报告...</span>
      </div>
    );
  }

  const renderChange = (
    changeNumber: number,
    precision: number,
    unit: string
  ) => {
    if (!changeNumber) {
      return null;
    }
    const isPositive = changeNumber > 0;
    const isNegative = changeNumber < 0;
    const color = isPositive
      ? 'text-green-500'
      : isNegative
        ? 'text-red-500'
        : 'text-gray-500';
    const icon = isPositive ? (
      <ArrowUpCircle size={14} />
    ) : isNegative ? (
      <ArrowDownCircle size={14} />
    ) : null;
    const diffText = isPositive
      ? `+${changeNumber.toFixed(precision)}`
      : changeNumber.toFixed(precision);

    return (
      <span className={`ml-1.5 inline-flex items-center text-xs ${color}`}>
        {icon}
        <span className="ml-0.5">
          {diffText}
          {unit}
        </span>
      </span>
    );
  };
  const parseLine = (text: string) => {
    let result = text;

    // 处理粗体
    result = result.replace(/\*\*(.*?)\*\*/g, (match, p1) => (
      <strong className="font-bold" key={match}>
        {p1}
      </strong>
    ));

    // 处理斜体
    result = result.replace(/\*(.*?)\*/g, (match, p1) => (
      <em className="italic" key={match}>
        {p1}
      </em>
    ));

    // 处理链接
    result = result.replace(/\[(.*?)\]\((.*?)\)/g, (match, text, url) => (
      <a
        className="text-blue-600 underline hover:text-blue-800"
        href={url}
        key={match}
      >
        {text}
      </a>
    ));
    return result;
  };
  const parseMarkdown = (markdown: string) => {
    if (!markdown) return;
    // 将文本分割成行
    return markdown.split('\n').map((line, index) => {
      // 处理标题
      if (line.startsWith('# ')) {
        return (
          <h1 className="mb-4 font-bold text-3xl" key={index.toString()}>
            {line.slice(2)}
          </h1>
        );
      }
      if (line.startsWith('## ')) {
        return (
          <h2 className="mb-3 font-bold text-2xl" key={index.toString()}>
            {line.slice(3)}
          </h2>
        );
      }
      if (line.startsWith('### ')) {
        return (
          <h3 className="mb-2 font-bold text-xl" key={index.toString()}>
            {line.slice(4)}
          </h3>
        );
      }
      // 处理列表
      if (line.startsWith('- ')) {
        return (
          <li className="ml-4" key={index.toString()}>
            {parseLine(line.slice(2))}
          </li>
        );
      }
      // 处理空行
      if (line.trim() === '') {
        return <br key={index.toString()} />;
      }

      // 处理普通段落
      return (
        <p className="mb-2" key={index.toString()}>
          {parseLine(line)}
        </p>
      );
    });
  };

  return (
    <div
      className="min-h-screen w-[8.5in] bg-white px-6 print:px-0"
      data-loaded={loading}
    >
      <div className="mb-8 text-center">
        <h1 className="mb-2 font-bold text-2xl text-gray-900">幼儿评价报告</h1>
        <p className="font-medium text-gray-600 text-lg">{studentName}</p>
      </div>

      <div className="mb-4 rounded-lg bg-gray-50 p-6">
        <div className="flex flex-row items-center">
          <p className="text-gray-500 text-sm">报告范围：</p>
          <p className="text-gray-500 text-sm">
            {reportData.studentReport?.startDate}至
            {reportData.studentReport?.endDate}
          </p>
        </div>
        <div className="my-2 flex flex-row items-center">
          <p className="text-gray-500 text-sm">参与人员：</p>
          <p className="text-gray-500 text-sm">
            {reportData.studentReport?.evaluator}
          </p>
        </div>
        <div className="flex flex-row items-center">
          <p className="text-gray-500 text-sm">观察记录：</p>
          <p className="text-gray-500 text-sm">
            {reportData.studentReport?.observationCnt}条
          </p>
        </div>
      </div>
      <div className="space-y-4">
        <section>
          <h2 className="mb-4 border-gray-200 border-b pb-2 font-bold text-gray-900 text-xl">
            总体评价
          </h2>
          <p className="text-gray-700 leading-relaxed">
            {reportData.studentReport?.evaluation}
          </p>
        </section>
        <section>
          <h2 className="mb-4 border-gray-200 border-b pb-2 font-bold text-gray-900 text-xl">
            报告说明
          </h2>
          <p className="text-gray-700 leading-relaxed">
            本报告根据
            {dimensions.map((item) => {
              return (
                <span key={item}>
                  {dimensionsList.find((i) => i.dimensions === item)?.label}
                </span>
              );
            })}
            为指导依据，评价依据来源同步参考教师日常观察记录。评估指标包括：12个领域，22个子领域，共51项评估指标。适用于了解、分析3-6岁幼儿的全面发展状况。
          </p>
        </section>

        {/* 核心能力分析 */}
        <section>
          <h2 className="border-gray-200 border-b pb-2 font-bold text-gray-900 text-xl">
            核心能力分析
          </h2>

          {/* Radar Chart */}
          <div className="mb-2 rounded-lg bg-white p-4">
            <h3 className="mb-4 font-semibold text-gray-800 text-lg">
              3-6岁儿童学习与发展指南
            </h3>
            <div style={{ height: '300px', width: '100%' }}>
              <ResponsiveContainer height="100%" width="100%">
                <RadarChart data={fieldRadarChart} outerRadius="80%">
                  <PolarGrid stroke="#e5e7eb" />
                  <PolarAngleAxis
                    dataKey="subject"
                    tick={{ fill: '#666', fontSize: 16 }}
                  />
                  <PolarRadiusAxis
                    angle={18}
                    domain={[0, 100]}
                    tick={{ fill: '#999', fontSize: 10 }}
                  />
                  <Radar
                    dataKey="score"
                    fill="#3b82f6"
                    fillOpacity={0.4}
                    name="当前表现"
                    stroke="#3b82f6"
                  />
                  <Legend />
                </RadarChart>
              </ResponsiveContainer>
            </div>
          </div>
          <div className="rounded-lg bg-white px-4">
            <h3 className="mb-2 font-semibold text-gray-800 text-lg">
              幸福感和参与度指标
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="rounded-lg bg-white p-4">
                <h4 className="mb-2 font-medium text-gray-500 text-sm">
                  幸福感
                </h4>
                <p className="font-bold text-2xl text-blue-600">
                  {reportData.happinessEngagement?.happinessAvg}/
                  {reportData.happinessEngagement?.happinessTotalScore}分
                </p>
              </div>
              <div className="rounded-lg bg-white p-4">
                <h4 className="mb-2 font-medium text-gray-500 text-sm">
                  参与度
                </h4>
                <p className="font-bold text-2xl text-blue-600">
                  {reportData.happinessEngagement?.engagementAvg}/
                  {reportData.happinessEngagement?.engagementTotalScore}分
                </p>
              </div>
            </div>
          </div>
          {dimensions.includes('1') && (
            <div className="rounded-lg bg-white px-4">
              <h3 className="mb-2 font-semibold text-gray-800 text-lg">
                深度学习能力
              </h3>
              <Grid columns={3} gap={12}>
                {(
                  Object.keys(
                    reportData.deepLearningCounters
                  ) as (keyof typeof reportData.deepLearningCounters)[]
                ).map((key) => (
                  <Grid.Item key={key}>
                    <div className="mb-2">
                      {/* 将 key 转换为更友好的名称 */}
                      <div className="text-gray-500 text-xs capitalize">
                        {ability[key]}
                      </div>
                      <div className="font-medium text-gray-700 text-xl">
                        {reportData.deepLearningCounters[key]} 次
                      </div>
                    </div>
                  </Grid.Item>
                ))}
              </Grid>
            </div>
          )}
        </section>
        <section>
          <h2 className="border-gray-200 border-b pt-6 pb-2 font-bold text-gray-900 text-xl">
            能力纵向变化
          </h2>
          <div className="mb-2 rounded-lg bg-white p-4">
            <div style={{ height: '300px', width: '100%' }}>
              <ResponsiveContainer height="100%" width="100%">
                {/* 调整对比雷达图颜色 */}
                <RadarChart data={abilityRadar} outerRadius="80%">
                  <PolarGrid stroke="#e0e0e0" />
                  <PolarAngleAxis
                    dataKey="subject"
                    tick={{ fill: '#666', fontSize: 16 }}
                  />
                  <PolarRadiusAxis
                    angle={18}
                    domain={[0, 100]}
                    tick={{ fill: '#999', fontSize: 10 }}
                  />
                  <Radar
                    dataKey="current"
                    fill="#6366f1"
                    fillOpacity={0.6} // 蓝色
                    name="当前表现"
                    stroke="#6366f1"
                  />
                  <Radar
                    dataKey="last"
                    fill="#f59e0b"
                    fillOpacity={0.3} // 绿色
                    name="上次表现"
                    stroke="#f59e0b" // 降低上次表现的透明度
                  />
                  <Legend
                    wrapperStyle={{ fontSize: '12px', paddingTop: '10px' }}
                  />
                </RadarChart>
              </ResponsiveContainer>
            </div>
          </div>
          <div className="mb-2 rounded-lg bg-white p-4">
            <h3 className="mb-4 font-semibold text-gray-800 text-lg">
              幸福感和参与度变化
            </h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-gray-500 text-xs">幸福感</div>
                <div className="flex items-center justify-center font-semibold text-blue-600 text-xl">
                  {reportData.happinessEngagement?.happinessAvg}
                  {/* 使用辅助函数渲染变化 */}
                  {renderChange(
                    reportData.indicatorChanges?.happinessAvg,
                    2,
                    '分'
                  )}
                </div>
              </div>
              <div className="text-center">
                <div className="text-gray-500 text-xs">参与度</div>
                <div className="flex items-center justify-center font-semibold text-blue-600 text-lg">
                  {reportData.happinessEngagement?.engagementAvg}
                  {renderChange(
                    reportData.indicatorChanges?.engagementAvg,
                    2,
                    '分'
                  )}
                </div>
              </div>
            </div>
          </div>
          {dimensions.includes('1') && (
            <div className="mb-2 rounded-lg bg-white p-4">
              <h3 className="mb-4 font-semibold text-gray-800 text-lg">
                深度学习能力变化
              </h3>
              <Grid columns={3} gap={12}>
                {/* 渲染各项能力变化 */}
                {(
                  Object.keys(
                    reportData.deepLearningCounters
                  ) as (keyof typeof reportData.deepLearningCounters)[]
                ).map((key) => (
                  <Grid.Item key={key}>
                    <div className="mb-2">
                      <div className="text-gray-500 text-xs capitalize">
                        {ability[key]}
                      </div>
                      <div className="flex items-center font-medium text-gray-700 text-xl">
                        {reportData.deepLearningCounters[key]} 次
                        {renderChange(
                          reportData.indicatorChanges[key],
                          0,
                          '次'
                        )}
                      </div>
                    </div>
                  </Grid.Item>
                ))}
              </Grid>
            </div>
          )}
        </section>
        {/* 总结与建议 */}
        <section>
          <h2 className="border-gray-200 border-b pb-2 font-bold text-gray-900 text-xl">
            总结与建议
          </h2>

          <div className="space-y-2">
            <div className="rounded-lg bg-white px-6 pt-3 pb-2">
              <h3 className="mb-3 font-semibold text-gray-800 text-lg">
                优势领域
              </h3>
              <div className="text-gray-700">
                {parseMarkdown(reportData.studentReport?.advantages)}
              </div>
            </div>

            <div className="rounded-lg bg-white px-6 pt-3 pb-2">
              <h3 className="mb-3 font-semibold text-gray-800 text-lg">
                成长机会
              </h3>
              <div className="text-gray-700">
                {parseMarkdown(reportData.studentReport?.disadvantages)}
              </div>
            </div>
            <div className="rounded-lg bg-white px-6 pt-3 pb-2">
              <h3 className="mb-3 font-semibold text-gray-800 text-lg">
                个性化教学策略
              </h3>
              <div className="text-gray-700">
                {parseMarkdown(
                  reportData.studentReport?.personalizedTeachingStrategy
                )}
              </div>
            </div>
            <div className="rounded-lg bg-white px-6 pt-3 pb-2">
              <h3 className="mb-3 font-semibold text-gray-800 text-lg">
                教师支持策略
              </h3>
              <div className="text-gray-700">
                {parseMarkdown(
                  reportData.studentReport?.teacherSupportStrategy
                )}
              </div>
            </div>
            <div className="rounded-lg bg-white px-6 pt-3 pb-2">
              <h3 className="mb-3 font-semibold text-gray-800 text-lg">
                家园共育合作
              </h3>
              <div className="text-gray-700">
                {parseMarkdown(reportData.studentReport?.homeSchoolCooperation)}
              </div>
            </div>
            <div className="rounded-lg bg-white px-6 pt-3 pb-2">
              <h3 className="mb-3 font-semibold text-gray-800 text-lg">
                教师寄语
              </h3>
              <div className="text-gray-700">
                {parseMarkdown(reportData.studentReport?.teacherNote)}
              </div>
            </div>
          </div>
        </section>
        <footer className="mt-12 pt-6 text-center text-gray-500 text-sm">
          <p>报告生成时间：{new Date().toLocaleDateString()}</p>
        </footer>
      </div>
    </div>
  );
}
