'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  DotLoading,
  <PERSON>rid,
  <PERSON><PERSON>,
  Ta<PERSON>,
  Toast,
} from 'antd-mobile';
import Cookies from 'js-cookie';
import { ArrowDownCircle, ArrowUpCircle, Edit2 } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import {
  Legend,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
} from 'recharts';

import {
  getObservationDimensions,
  getStudentReportDetail,
  updateStudentReport,
} from '@/api/pbl';
import { share } from '@/utils/index';

export default function GenerateReportPage() {
  if (typeof window !== 'undefined') {
    document.title = '报告详情';
  }
  const searchParams = useSearchParams();
  const reportId = searchParams.get('reportId');
  const [loading, setLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [studentName, setStudentName] = useState('');
  const [exportModal, setExportModal] = useState(false);
  const [pdfUrl, setPdfUrl] = useState('');
  const [ability] = useState<any>({
    communication: '沟通能力',
    collaboration: '协作能力',
    creativity: '创造力',
    criticalThinking: '批判性思维',
    citizenshipCharacterEducation: '公民意识与品格',
  });
  const [dimensions, setDimensions] = useState<string[]>([]);
  const [fieldRadarChart, setFieldRadarChart] = useState([
    { subject: '健康', score: 1, fullMark: 100, field: 'healthPct' },
    { subject: '语言', score: 2, fullMark: 100, field: 'languageFullPct' },
    { subject: '社会', score: 4, fullMark: 100, field: 'societyPct' },
    { subject: '科学', score: 3, fullMark: 100, field: 'sciencePct' },
    { subject: '艺术', score: 2, fullMark: 100, field: 'artsFullPct' },
  ]);
  const [abilityRadar, setAbilityRadar] = useState([
    {
      subject: '健康 ',
      current: 92,
      last: 90,
      fullMark: 100,
      field: 'healthPct',
    },

    {
      subject: '语言',
      current: 80,
      last: 90,
      fullMark: 100,
      field: 'languageFullPct',
    },
    {
      subject: '社会',
      current: 64,
      last: 85,
      fullMark: 100,
      field: 'societyPct',
    },
    {
      subject: '科学',
      current: 83,
      last: 95,
      fullMark: 100,
      field: 'sciencePct',
    },

    {
      subject: '艺术',
      current: 72,
      last: 75,
      fullMark: 100,
      field: 'artsFullPct',
    },
  ]);
  const [reportData, setReportData] = useState<any>({
    deepLearningCounters: {},
  });
  const [isEditingAdvantages, setIsEditingAdvantages] = useState(false);
  const [isEditingDisadvantages, setIsEditingDisadvantages] = useState(false);
  const [isEditingPersonalizedStrategy, setIsEditingPersonalizedStrategy] =
    useState(false);
  const [isEditingTeacherSupport, setIsEditingTeacherSupport] = useState(false);
  const [isEditingHomeSchool, setIsEditingHomeSchool] = useState(false);
  const [isEditingTeacherNote, setIsEditingTeacherNote] = useState(false);
  const [isEditingEvaluation, setIsEditingEvaluation] = useState(false);

  const [editedAdvantages, setEditedAdvantages] = useState('');
  const [editedDisadvantages, setEditedDisadvantages] = useState('');
  const [editedPersonalizedStrategy, setEditedPersonalizedStrategy] =
    useState('');
  const [editedTeacherSupport, setEditedTeacherSupport] = useState('');
  const [editedHomeSchool, setEditedHomeSchool] = useState('');
  const [editedTeacherNote, setEditedTeacherNote] = useState('');
  const [editedEvaluation, setEditedEvaluation] = useState('');
  const [dimensionsList] = useState([
    {
      dimensions: '2',
      label: '1. 《3-6岁儿童学习与发展指南》',
      value: '评估指标包括：5个领域，12个子领域，共36项评估指标。',
    },
    {
      dimensions: '3',
      label: '2. 《幸福感和参与度指标》',
      value: '评估指标包括：2个领域，10个子领域，共10项评估指标。',
    },
    {
      dimensions: '1',
      label: '3. 《深度学习能力指标》',
      value: '评估指标包括：5个领域，12个子领域，共36项评估指标。',
    },
  ]);
  useEffect(() => {
    setLoading(true);
    const fetchReportData = async () => {
      try {
        const response = (await getStudentReportDetail({ reportId })) as any;
        setStudentName(response.studentReport.studentName);
        const fieldRadarChartTemp = fieldRadarChart.map((item) => {
          return {
            ...item,
            score: response.radarChart[item.field],
          };
        });
        const abilityRadarChartTemp = abilityRadar.map((item) => {
          return {
            ...item,
            current: response.radarChart[item.field],
            last: response.prefRadarChart?.[item.field] ?? 0,
          };
        });
        setFieldRadarChart(fieldRadarChartTemp);
        setAbilityRadar(abilityRadarChartTemp);
        setReportData(response);
        setLoading(false);
      } catch (error) {
        setLoading(false);
      }
    };

    if (reportId) {
      fetchReportData();
    }
    getObservationDimensions().then((res: any) => {
      setDimensions(res.dimensions);
    });
  }, [reportId]);

  useEffect(() => {
    if (reportData.studentReport) {
      setEditedAdvantages(reportData.studentReport.advantages || '');
      setEditedDisadvantages(reportData.studentReport.disadvantages || '');
      setEditedPersonalizedStrategy(
        reportData.studentReport.personalizedTeachingStrategy || ''
      );
      setEditedTeacherSupport(
        reportData.studentReport.teacherSupportStrategy || ''
      );
      setEditedHomeSchool(reportData.studentReport.homeSchoolCooperation || '');
      setEditedTeacherNote(reportData.studentReport.teacherNote || '');
      setEditedEvaluation(reportData.studentReport.evaluation || '');
    }
  }, [reportData.studentReport]);

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50 p-4">
        <DotLoading color="primary" />
        <span className="mt-2 ml-2 text-gray-500">正在打开评价报告...</span>
      </div>
    );
  }

  const renderChange = (
    changeNumber: number,
    precision: number,
    unit: string
  ) => {
    if (!changeNumber) {
      return null;
    }
    const isPositive = changeNumber > 0;
    const isNegative = changeNumber < 0;
    const color = isPositive
      ? 'text-green-500'
      : isNegative
        ? 'text-red-500'
        : 'text-gray-500';
    const icon = isPositive ? (
      <ArrowUpCircle size={14} />
    ) : isNegative ? (
      <ArrowDownCircle size={14} />
    ) : null;
    const diffText = isPositive
      ? `+${changeNumber.toFixed(precision)}`
      : changeNumber.toFixed(precision);

    return (
      <span className={`ml-1.5 inline-flex items-center text-xs ${color}`}>
        {icon}
        <span className="ml-0.5">
          {diffText}
          {unit}
        </span>
      </span>
    );
  };

  const handleExport = async () => {
    const token = Cookies.get('Authorization');
    try {
      setExportLoading(true);
      // 构建当前页面的完整URL
      const currentUrl = `${window.location.origin}/pbl/record/report/studentDetail/export?reportId=${reportId}`;
      console.log('currentUrl: ', currentUrl);
      // 调用PDF生成API
      const response = await fetch(
        `/api/record/export?url=${encodeURIComponent(currentUrl)}&reportKey=${token}&sourceType=2`
      );
      if (!response.ok) {
        throw new Error('PDF生成失败');
      }
      const data = await response.json();
      // 保存PDF链接并显示弹窗
      if (data.url) {
        setPdfUrl(data.url);
        setExportModal(true);
      } else {
        throw new Error('未获取到PDF链接');
      }
    } catch (error) {
      Toast.show({
        content: `导出报告失败，请稍后重试:${error.message ?? error}`,
        position: 'center',
      });
    } finally {
      setExportLoading(false);
    }
  };

  // 分享功能
  const handleShare = () => {
    share({
      type: 0,
      title: `${studentName} 幼儿评价报告`,
      thumbImage:
        'https://unicorn-media.ancda.com/prod/discover/fileIcon/file-type-pdf.svg',
      description: `快来看看${studentName}幼儿评价报告`,
      url: pdfUrl,
    });
  };
  const parseLine = (text: string) => {
    let result = text;

    // 处理粗体
    result = result.replace(/\*\*(.*?)\*\*/g, (match, p1) => (
      <strong className="font-bold" key={match}>
        {p1}
      </strong>
    ));

    // 处理斜体
    result = result.replace(/\*(.*?)\*/g, (match, p1) => (
      <em className="italic" key={match}>
        {p1}
      </em>
    ));

    // 处理链接
    result = result.replace(/\[(.*?)\]\((.*?)\)/g, (match, text, url) => (
      <a
        className="text-blue-600 underline hover:text-blue-800"
        href={url}
        key={match}
      >
        {text}
      </a>
    ));
    return result;
  };
  const parseMarkdown = (markdown: string) => {
    if (!markdown) return;
    // 将文本分割成行
    return markdown.split('\n').map((line, index) => {
      // 处理标题
      if (line.startsWith('# ')) {
        return (
          <h1 className="mb-4 font-bold text-3xl" key={index.toString()}>
            {line.slice(2)}
          </h1>
        );
      }
      if (line.startsWith('## ')) {
        return (
          <h2 className="mb-3 font-bold text-2xl" key={index.toString()}>
            {line.slice(3)}
          </h2>
        );
      }
      if (line.startsWith('### ')) {
        return (
          <h3 className="mb-2 font-bold text-xl" key={index.toString()}>
            {line.slice(4)}
          </h3>
        );
      }
      // 处理列表
      if (line.startsWith('- ')) {
        return (
          <li className="ml-4" key={index.toString()}>
            {parseLine(line.slice(2))}
          </li>
        );
      }
      // 处理空行
      if (line.trim() === '') {
        return <br key={index.toString()} />;
      }

      // 处理普通段落
      return (
        <p className="mb-2" key={index.toString()}>
          {parseLine(line)}
        </p>
      );
    });
  };

  const handleSaveSection = async (section: string) => {
    if (!reportId) {
      Toast.show({
        content: '报告ID不存在',
        position: 'center',
      });
      return;
    }

    try {
      const data = {
        reportId,
        advantages: editedAdvantages,
        disadvantages: editedDisadvantages,
        personalizedTeachingStrategy: editedPersonalizedStrategy,
        teacherSupportStrategy: editedTeacherSupport,
        homeSchoolCooperation: editedHomeSchool,
        teacherNote: editedTeacherNote,
        evaluation: editedEvaluation,
        title: reportData.studentReport?.title || '',
        evaluator: reportData.studentReport?.evaluator || '',
      };

      await updateStudentReport(data);

      // Reset editing state for the specific section
      switch (section) {
        case 'advantages':
          setEditedAdvantages(editedAdvantages || '');
          setIsEditingAdvantages(false);
          break;
        case 'disadvantages':
          setEditedDisadvantages(editedDisadvantages || '');
          setIsEditingDisadvantages(false);
          break;
        case 'personalizedStrategy':
          setEditedPersonalizedStrategy(editedPersonalizedStrategy || '');
          setIsEditingPersonalizedStrategy(false);
          break;
        case 'teacherSupport':
          setEditedTeacherSupport(editedTeacherSupport || '');
          setIsEditingTeacherSupport(false);
          break;
        case 'homeSchool':
          setEditedHomeSchool(editedHomeSchool || '');
          setIsEditingHomeSchool(false);
          break;
        case 'teacherNote':
          setEditedTeacherNote(editedTeacherNote || '');
          setIsEditingTeacherNote(false);
          break;
        case 'evaluation':
          setEditedEvaluation(editedEvaluation || '');
          setIsEditingEvaluation(false);
          break;
      }

      Toast.show({
        content: '保存成功',
        position: 'center',
      });
    } catch (error) {
      Toast.show({
        content: '保存失败，请重试',
        position: 'center',
      });
    }
  };

  const handleCancelEdit = (section: string) => {
    // Reset the edited content to the original value
    switch (section) {
      case 'advantages':
        setEditedAdvantages(reportData.studentReport?.advantages || '');
        setIsEditingAdvantages(false);
        break;
      case 'disadvantages':
        setEditedDisadvantages(reportData.studentReport?.disadvantages || '');
        setIsEditingDisadvantages(false);
        break;
      case 'personalizedStrategy':
        setEditedPersonalizedStrategy(
          reportData.studentReport?.personalizedTeachingStrategy || ''
        );
        setIsEditingPersonalizedStrategy(false);
        break;
      case 'teacherSupport':
        setEditedTeacherSupport(
          reportData.studentReport?.teacherSupportStrategy || ''
        );
        setIsEditingTeacherSupport(false);
        break;
      case 'homeSchool':
        setEditedHomeSchool(
          reportData.studentReport?.homeSchoolCooperation || ''
        );
        setIsEditingHomeSchool(false);
        break;
      case 'teacherNote':
        setEditedTeacherNote(reportData.studentReport?.teacherNote || '');
        setIsEditingTeacherNote(false);
        break;
      case 'evaluation':
        setEditedEvaluation(reportData.studentReport?.evaluation || '');
        setIsEditingEvaluation(false);
        break;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 pb-16" data-loaded={loading}>
      <h1 className="mb-6 text-center font-semibold text-blue-500 text-xl">
        {studentName} 幼儿评价报告
      </h1>
      <Card className="mb-6 rounded-lg shadow-sm">
        <div className="space-y-2 text-gray-600 text-sm">
          <p>
            <strong>报告范围：</strong>
            {reportData.studentReport?.startDate} 至
            {reportData.studentReport?.endDate}
          </p>
          <p>
            <strong>参与人员：</strong>
            {reportData.studentReport?.evaluator}
          </p>
          <p>
            <strong>观察记录：</strong>
            {reportData.studentReport?.observationCnt}条
          </p>
        </div>
        <Divider className="my-4" contentPosition="left">
          <span className="font-medium text-base text-gray-600">报告说明</span>
        </Divider>
        <p className="mb-4 text-gray-500 text-sm">
          本报告根据
          {dimensions.map((item) => {
            return (
              <span key={item}>
                {dimensionsList.find((i) => i.dimensions === item)?.label}
              </span>
            );
          })}
          为指导依据，评价依据来源同步参考教师日常观察记录。评估指标包括：12个领域，22个子领域，共51项评估指标。适用于了解、分析3-6岁幼儿的全面发展状况。
        </p>
        <div
          className="group flex cursor-pointer flex-col items-center rounded-t-lg border border-gray-200"
          onClick={() => !isEditingEvaluation && setIsEditingEvaluation(true)}
        >
          <div className="flex w-full flex-row justify-between bg-blue-50 px-3 py-2">
            <span className="flex items-center font-medium text-base text-gray-600 transition-colors group-hover:text-blue-600">
              总体评价
            </span>
            {isEditingEvaluation ? (
              <div className="flex gap-2">
                <Button
                  fill="none"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCancelEdit('evaluation');
                  }}
                  size="small"
                >
                  取消
                </Button>
                <Button
                  color="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSaveSection('evaluation');
                  }}
                  size="small"
                >
                  保存
                </Button>
              </div>
            ) : (
              <div
                className="flex flex-row items-center"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsEditingEvaluation(true);
                }}
              >
                <span className="text-blue-400 text-sm hover:text-blue-700">
                  编辑
                </span>
                <Edit2
                  className="ml-2 text-blue-400 group-hover:text-blue-600"
                  size={16}
                />
              </div>
            )}
          </div>
          {isEditingEvaluation ? (
            <textarea
              className="w-full rounded-lg border border-gray-200 bg-white p-3 text-sm shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              onChange={(e) => setEditedEvaluation(e.target.value)}
              placeholder="请输入总体评价内容..."
              rows={8}
              value={editedEvaluation}
            />
          ) : (
            <div className="flex items-center justify-between px-3 py-2">
              <p className="mb-0 text-gray-600 text-sm">
                {reportData.studentReport?.evaluation}
              </p>
            </div>
          )}
        </div>

        <Tabs className="mt-4" defaultActiveKey="1">
          <Tabs.Tab key="1" title="核心能力分析">
            <Card
              className="my-4 rounded-lg border border-gray-100 shadow-none"
              title={
                <span className="font-medium text-gray-600">
                  3-6岁儿童学习与发展指南
                </span>
              }
            >
              <div style={{ height: '300px', width: '100%' }}>
                <ResponsiveContainer height="100%" width="100%">
                  <RadarChart data={fieldRadarChart} outerRadius="80%">
                    <PolarGrid stroke="#e0e0e0" />
                    <PolarAngleAxis
                      dataKey="subject"
                      tick={{ fill: '#666', fontSize: 14 }}
                    />
                    <PolarRadiusAxis
                      angle={18}
                      domain={[0, 100]}
                      tick={{ fill: '#999', fontSize: 10 }}
                    />
                    <Radar
                      dataKey="score"
                      fill="#6366f1"
                      fillOpacity={0.6}
                      name="当前表现"
                      stroke="#6366f1"
                    />
                    <Legend
                      wrapperStyle={{ fontSize: '12px', paddingTop: '10px' }}
                    />
                  </RadarChart>
                </ResponsiveContainer>
              </div>
            </Card>

            <Card
              className="mb-4 rounded-lg border border-gray-100 shadow-none"
              title={
                <span className="font-medium text-gray-600">
                  幸福感和参与度指标
                </span>
              }
            >
              <Grid columns={2} gap={16}>
                <Grid.Item>
                  <div className="text-center">
                    <div className="text-gray-500 text-xs">幸福感</div>
                    <div className="font-semibold text-blue-600 text-lg">
                      {reportData.happinessEngagement?.happinessAvg}/ 5分
                    </div>
                  </div>
                </Grid.Item>
                <Grid.Item>
                  <div className="text-center">
                    <div className="text-gray-500 text-xs">参与度</div>
                    <div className="font-semibold text-blue-600 text-lg">
                      {reportData.happinessEngagement?.engagementAvg}/ 5分
                    </div>
                  </div>
                </Grid.Item>
              </Grid>
            </Card>

            {dimensions.includes('1') && (
              <Card
                className="mb-4 rounded-lg border border-gray-100 shadow-none"
                title={
                  <span className="font-medium text-gray-600">
                    深度学习能力
                  </span>
                }
              >
                <Grid columns={2} gap={12}>
                  {(
                    Object.keys(
                      reportData.deepLearningCounters
                    ) as (keyof typeof reportData.deepLearningCounters)[]
                  ).map((key) => (
                    <Grid.Item key={key}>
                      <div className="mb-2">
                        <div className="text-gray-500 text-xs capitalize">
                          {ability[key]}
                        </div>
                        <div className="font-medium text-base text-gray-700">
                          {reportData.deepLearningCounters[key]} 次
                        </div>
                      </div>
                    </Grid.Item>
                  ))}
                </Grid>
              </Card>
            )}
          </Tabs.Tab>

          <Tabs.Tab key="2" title="能力纵向变化">
            <Card className="my-4 rounded-lg border border-gray-100 shadow-none">
              <div style={{ height: '300px', width: '100%' }}>
                <ResponsiveContainer height="100%" width="100%">
                  <RadarChart data={abilityRadar} outerRadius="80%">
                    <PolarGrid stroke="#e0e0e0" />
                    <PolarAngleAxis
                      dataKey="subject"
                      tick={{ fill: '#666', fontSize: 12 }}
                    />
                    <PolarRadiusAxis
                      angle={18}
                      domain={[0, 100]}
                      tick={{ fill: '#999', fontSize: 10 }}
                    />
                    <Radar
                      dataKey="current"
                      fill="#6366f1"
                      fillOpacity={0.6}
                      name="本次报告"
                      stroke="#6366f1"
                    />
                    <Radar
                      dataKey="last"
                      fill="#f59e0b"
                      fillOpacity={0.3}
                      name="上次报告"
                      stroke="#f59e0b"
                    />
                    <Legend
                      wrapperStyle={{ fontSize: '12px', paddingTop: '10px' }}
                    />
                  </RadarChart>
                </ResponsiveContainer>
              </div>
            </Card>

            <Card
              className="mb-4 rounded-lg border border-gray-100 shadow-none"
              title={
                <span className="font-medium text-gray-600">
                  幸福感和参与度变化
                </span>
              }
            >
              <Grid columns={2} gap={16}>
                <Grid.Item>
                  <div className="text-center">
                    <div className="text-gray-500 text-xs">幸福感</div>
                    <div className="flex items-center justify-center font-semibold text-blue-600 text-lg">
                      {reportData.happinessEngagement?.happinessAvg}
                      {renderChange(
                        reportData.indicatorChanges?.happinessAvg,
                        2,
                        '分'
                      )}
                    </div>
                  </div>
                </Grid.Item>
                <Grid.Item>
                  <div className="text-center">
                    <div className="text-gray-500 text-xs">参与度</div>
                    <div className="flex items-center justify-center font-semibold text-blue-600 text-lg">
                      {reportData.happinessEngagement?.engagementAvg}
                      {renderChange(
                        reportData.indicatorChanges?.engagementAvg,
                        2,
                        '分'
                      )}
                    </div>
                  </div>
                </Grid.Item>
              </Grid>
            </Card>

            {dimensions.includes('1') && (
              <Card
                className="mb-4 rounded-lg border border-gray-100 shadow-none"
                title={
                  <span className="font-medium text-gray-600">
                    深度学习能力变化
                  </span>
                }
              >
                <Grid columns={2} gap={12}>
                  {(
                    Object.keys(
                      reportData.deepLearningCounters
                    ) as (keyof typeof reportData.deepLearningCounters)[]
                  ).map((key) => (
                    <Grid.Item key={key}>
                      <div className="mb-2">
                        <div className="text-gray-500 text-xs capitalize">
                          {ability[key]}
                        </div>
                        <div className="flex items-center font-medium text-base text-gray-700">
                          {reportData.deepLearningCounters[key]} 次
                          {renderChange(
                            reportData.indicatorChanges[key],
                            0,
                            '次'
                          )}
                        </div>
                      </div>
                    </Grid.Item>
                  ))}
                </Grid>
              </Card>
            )}
          </Tabs.Tab>

          <Tabs.Tab key="3" title="总结与建议">
            <Card
              className="my-4 rounded-lg border border-gray-100 shadow-none"
              extra={
                isEditingAdvantages ? (
                  <div className="flex gap-2">
                    <Button
                      fill="none"
                      onClick={() => handleCancelEdit('advantages')}
                      size="small"
                    >
                      取消
                    </Button>
                    <Button
                      color="primary"
                      onClick={() => handleSaveSection('advantages')}
                      size="small"
                    >
                      保存
                    </Button>
                  </div>
                ) : (
                  <div
                    className="flex flex-row items-center"
                    onClick={() => setIsEditingAdvantages(true)}
                  >
                    <span className="text-blue-400 text-sm group-hover:text-blue-600">
                      编辑
                    </span>
                    <Edit2
                      className="ml-2 text-blue-400 group-hover:text-blue-600"
                      size={16}
                    />
                  </div>
                )
              }
              title={
                <span className="font-medium text-gray-600">优势领域</span>
              }
            >
              {isEditingAdvantages ? (
                <textarea
                  className="w-full rounded-lg border border-gray-200 p-2 text-sm"
                  onChange={(e) => setEditedAdvantages(e.target.value)}
                  placeholder="请输入优势领域内容..."
                  rows={16}
                  value={editedAdvantages}
                />
              ) : (
                <div className="flex flex-wrap gap-2 text-sm">
                  {parseMarkdown(reportData.studentReport?.advantages)}
                </div>
              )}
            </Card>

            <Card
              className="mb-4 rounded-lg border border-gray-100 text-sm shadow-none"
              extra={
                isEditingDisadvantages ? (
                  <div className="flex gap-2">
                    <Button
                      fill="none"
                      onClick={() => handleCancelEdit('disadvantages')}
                      size="small"
                    >
                      取消
                    </Button>
                    <Button
                      color="primary"
                      onClick={() => handleSaveSection('disadvantages')}
                      size="small"
                    >
                      保存
                    </Button>
                  </div>
                ) : (
                  <div
                    className="flex flex-row items-center"
                    onClick={() => setIsEditingDisadvantages(true)}
                  >
                    <span className="text-blue-400 text-sm group-hover:text-blue-600">
                      编辑
                    </span>
                    <Edit2
                      className="ml-2 text-blue-400 group-hover:text-blue-600"
                      size={16}
                    />
                  </div>
                )
              }
              title={
                <span className="font-medium text-gray-600">成长机会</span>
              }
            >
              {isEditingDisadvantages ? (
                <textarea
                  className="w-full rounded-lg border border-gray-200 p-2 text-sm"
                  onChange={(e) => setEditedDisadvantages(e.target.value)}
                  placeholder="请输入成长机会内容..."
                  rows={16}
                  value={editedDisadvantages}
                />
              ) : (
                <div className="flex flex-wrap gap-2">
                  {parseMarkdown(reportData.studentReport?.disadvantages)}
                </div>
              )}
            </Card>

            <Card
              className="mb-4 rounded-lg border border-gray-100 text-sm shadow-none"
              extra={
                isEditingPersonalizedStrategy ? (
                  <div className="flex gap-2">
                    <Button
                      fill="none"
                      onClick={() => handleCancelEdit('personalizedStrategy')}
                      size="small"
                    >
                      取消
                    </Button>
                    <Button
                      color="primary"
                      onClick={() => handleSaveSection('personalizedStrategy')}
                      size="small"
                    >
                      保存
                    </Button>
                  </div>
                ) : (
                  <div
                    className="flex flex-row items-center"
                    onClick={() => setIsEditingPersonalizedStrategy(true)}
                  >
                    <span className="text-blue-400 text-sm group-hover:text-blue-600">
                      编辑
                    </span>
                    <Edit2
                      className="ml-2 text-blue-400 group-hover:text-blue-600"
                      size={16}
                    />
                  </div>
                )
              }
              title={
                <span className="font-medium text-gray-600">
                  个性化教学策略
                </span>
              }
            >
              {isEditingPersonalizedStrategy ? (
                <textarea
                  className="w-full rounded-lg border border-gray-200 p-2 text-sm"
                  onChange={(e) =>
                    setEditedPersonalizedStrategy(e.target.value)
                  }
                  placeholder="请输入个性化教学策略内容..."
                  rows={16}
                  value={editedPersonalizedStrategy}
                />
              ) : (
                parseMarkdown(
                  reportData.studentReport?.personalizedTeachingStrategy
                )
              )}
            </Card>

            <Card
              className="mb-4 rounded-lg border border-gray-100 text-sm shadow-none"
              extra={
                isEditingTeacherSupport ? (
                  <div className="flex gap-2">
                    <Button
                      fill="none"
                      onClick={() => handleCancelEdit('teacherSupport')}
                      size="small"
                    >
                      取消
                    </Button>
                    <Button
                      color="primary"
                      onClick={() => handleSaveSection('teacherSupport')}
                      size="small"
                    >
                      保存
                    </Button>
                  </div>
                ) : (
                  <div
                    className="flex flex-row items-center text-sm"
                    onClick={() => setIsEditingTeacherSupport(true)}
                  >
                    <span className="text-blue-400 group-hover:text-blue-600">
                      编辑
                    </span>
                    <Edit2
                      className="ml-2 text-blue-400 group-hover:text-blue-600"
                      size={16}
                    />
                  </div>
                )
              }
              title={
                <span className="font-medium text-gray-600">教师支持策略</span>
              }
            >
              {isEditingTeacherSupport ? (
                <textarea
                  className="w-full rounded-lg border border-gray-200 p-2 text-sm"
                  onChange={(e) => setEditedTeacherSupport(e.target.value)}
                  placeholder="请输入教师支持策略内容..."
                  rows={16}
                  value={editedTeacherSupport}
                />
              ) : (
                parseMarkdown(reportData.studentReport?.teacherSupportStrategy)
              )}
            </Card>

            <Card
              className="mb-4 rounded-lg border border-gray-100 text-sm shadow-none"
              extra={
                isEditingHomeSchool ? (
                  <div className="flex gap-2">
                    <Button
                      fill="none"
                      onClick={() => handleCancelEdit('homeSchool')}
                      size="small"
                    >
                      取消
                    </Button>
                    <Button
                      color="primary"
                      onClick={() => handleSaveSection('homeSchool')}
                      size="small"
                    >
                      保存
                    </Button>
                  </div>
                ) : (
                  <div
                    className="flex flex-row items-center"
                    onClick={() => setIsEditingHomeSchool(true)}
                  >
                    <span className="text-blue-400 text-sm group-hover:text-blue-600">
                      编辑
                    </span>
                    <Edit2
                      className="ml-2 text-blue-400 group-hover:text-blue-600"
                      size={16}
                    />
                  </div>
                )
              }
              title={
                <span className="font-medium text-gray-600">家园共育合作</span>
              }
            >
              {isEditingHomeSchool ? (
                <textarea
                  className="w-full rounded-lg border border-gray-200 p-2 text-sm"
                  onChange={(e) => setEditedHomeSchool(e.target.value)}
                  placeholder="请输入家园共育合作内容..."
                  rows={16}
                  value={editedHomeSchool}
                />
              ) : (
                parseMarkdown(reportData.studentReport?.homeSchoolCooperation)
              )}
            </Card>

            <Card
              className="mb-4 rounded-lg border border-gray-100 shadow-none"
              extra={
                isEditingTeacherNote ? (
                  <div className="flex gap-2">
                    <Button
                      fill="none"
                      onClick={() => handleCancelEdit('teacherNote')}
                      size="small"
                    >
                      取消
                    </Button>
                    <Button
                      color="primary"
                      onClick={() => handleSaveSection('teacherNote')}
                      size="small"
                    >
                      保存
                    </Button>
                  </div>
                ) : (
                  <div
                    className="flex flex-row items-center"
                    onClick={() => setIsEditingTeacherNote(true)}
                  >
                    <span className="text-blue-400 text-sm group-hover:text-blue-600">
                      编辑
                    </span>
                    <Edit2
                      className="ml-2 text-blue-400 group-hover:text-blue-600"
                      size={16}
                    />
                  </div>
                )
              }
              title={
                <span className="font-medium text-gray-600">教师寄语</span>
              }
            >
              {isEditingTeacherNote ? (
                <textarea
                  className="w-full rounded-lg border border-gray-200 p-2 text-sm"
                  onChange={(e) => setEditedTeacherNote(e.target.value)}
                  placeholder="请输入教师寄语内容..."
                  rows={16}
                  value={editedTeacherNote}
                />
              ) : (
                <p className="text-gray-600 text-sm">
                  {parseMarkdown(editedTeacherNote)}
                </p>
              )}
            </Card>
          </Tabs.Tab>
        </Tabs>

        <div className="mt-8 flex justify-center">
          <Button
            className="rounded-full px-8 shadow-md"
            color="primary"
            fill="solid"
            loading={exportLoading}
            onClick={handleExport}
          >
            导出报告
          </Button>
        </div>
      </Card>

      <Modal
        actions={[]}
        closeOnAction
        closeOnMaskClick
        content={
          <div className="relative py-4">
            <div
              aria-label="Close modal"
              className="absolute top-0 right-0 cursor-pointer p-2"
              onClick={() => setExportModal(false)}
            >
              <svg
                fill="none"
                height="20"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>Close</title>
                <line x1="18" x2="6" y1="6" y2="18" />
                <line x1="6" x2="18" y1="6" y2="18" />
              </svg>
            </div>
            <div className="mb-4 text-center font-medium text-base">
              报告导出成功
            </div>
            <div className="mb-6 text-center text-gray-500 text-sm">
              您可以复制链接或直接分享报告
            </div>

            <div className="flex gap-3 px-2">
              <CopyToClipboard
                onCopy={(text: string, result: boolean) => {
                  Toast.show({ content: result ? '复制成功' : '复制失败' });
                }}
                text={pdfUrl}
              >
                <Button
                  className="flex-1 rounded-lg border-blue-600 bg-blue-50 text-blue-600"
                  color="primary"
                  fill="outline"
                >
                  复制链接
                </Button>
              </CopyToClipboard>
              <Button
                className="flex-1 rounded-lg bg-blue-600"
                color="primary"
                onClick={handleShare}
              >
                分享报告
              </Button>
            </div>
          </div>
        }
        visible={exportModal}
      />
    </div>
  );
}
