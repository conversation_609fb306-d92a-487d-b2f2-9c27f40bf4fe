'use client';

import { List } from 'antd-mobile';
import { BarChart3, MapPin, Tag } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect } from 'react';

interface SettingItem {
  key: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  path: string;
}

export default function SettingsPage() {
  const router = useRouter();

  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.title = '观察记录设置';
    }
  }, []);

  const settingsItems: SettingItem[] = [
    {
      key: 'area',
      title: '区域设置',
      description: '学校观察区域配置',
      icon: <MapPin className="h-5 w-5 text-blue-500" />,
      path: '/pbl/record/settings/area',
    },
    {
      key: 'tags',
      title: '标签设置',
      description: '管理观察标签配置',
      icon: <Tag className="h-5 w-5 text-green-500" />,
      path: '/pbl/record/settings/tags',
    },
    {
      key: 'scale',
      title: '观察指标配置',
      description: '配置观察记录的评估指标',
      icon: <BarChart3 className="h-5 w-5 text-orange-500" />,
      path: '/pbl/record/settings/scale',
    },
  ];

  const handleItemClick = useCallback(
    (path: string) => {
      router.push(path);
    },
    [router]
  );

  return (
    <main className="min-h-screen bg-slate-50">
      <div className="p-4">
        <h1 className="font-semibold text-gray-800 text-xl">观察记录设置</h1>
      </div>
      <List mode="card" style={{ '--border-inner': 'none' }}>
        {settingsItems.map((item) => (
          <List.Item
            className="!py-2"
            description={item.description}
            key={item.key}
            onClick={() => handleItemClick(item.path)}
            prefix={item.icon}
          >
            <div className="font-medium text-gray-800">{item.title}</div>
          </List.Item>
        ))}
      </List>
    </main>
  );
}
