'use client';

import { format } from 'date-fns';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { FaRegClock } from 'react-icons/fa';
import type { SummaryCardProps } from '../types';

export default function SummaryCard({ summary }: SummaryCardProps) {
  const router = useRouter();

  // Handle card click to navigate to detail
  const handleCardClick = () => {
    console.log('summary.status:', summary.status);
    if (summary.status === 'generating') {
      return;
    }
    router.push(`/pbl/summary/detail/${summary.summaryId}`);
  };

  // Format creation date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'yyyy-MM-dd HH:mm');
    } catch {
      return dateString;
    }
  };

  return (
    <button
      className={`w-full overflow-hidden rounded-2xl bg-white text-left shadow-sm transition-opacity ${
        summary.status === 'generating' ? 'opacity-60' : ''
      }`}
      disabled={summary.status === 'generating'}
      onClick={handleCardClick}
      type="button"
    >
      <div className="p-4">
        {/* Title */}
        <h3 className="mb-2 line-clamp-2 font-semibold text-lg">
          {summary.title}
        </h3>

        {/* Content preview */}
        <p className="mb-3 line-clamp-3 text-gray-700 text-sm">
          {summary.summary}
        </p>

        {/* Footer with creator and time */}
        <div className="flex items-center justify-between">
          <div className="flex items-center text-gray-600 text-sm">
            <div className="flex items-center">
              {/* Display user avatar and name */}
              {summary.createUser ? (
                <div className="flex items-center">
                  <div className="relative mr-2 h-6 w-6">
                    <Image
                      alt={summary.createUser.name}
                      className="rounded-full object-cover"
                      fill
                      src={summary.createUser.avatar}
                    />
                  </div>
                  <span>{summary.createUser.name}</span>
                </div>
              ) : (
                <span>用户 {summary.createUserId}</span>
              )}
            </div>
            <div className="mx-2">•</div>
            <div className="flex items-center">
              <FaRegClock className="mr-1 text-gray-500" />
              <span>{formatDate(summary.createTime)}</span>
            </div>
          </div>
        </div>
      </div>
    </button>
  );
}
