'use client';

import { useInfiniteQuery } from '@tanstack/react-query';
import { Button, Dialog, NavBar, Toast } from 'antd-mobile';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { createPeriodicSummary, getObservationList } from '@/api/pbl';
import type { RecordData } from '../../record/list/mock/recordData';
import RecordSelector from '../components/RecordSelector';
import AISummaryModal from './components/AISummaryModal';

export default function CreateSummaryPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const projectId = searchParams.get('projectId');

  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
  } = useInfiniteQuery({
    queryKey: ['observationList', projectId],
    queryFn: async ({ pageParam = 1 }) => {
      const { list = [], total = 0 } = (await getObservationList({
        projectId,
        page: pageParam,
        perPage: 20,
        isGenerating: 0,
      })) as { list?: RecordData[]; total?: number };
      console.log('🚀 ~ list:', list);

      return {
        records: list,
        total,
        page: pageParam,
        hasMore: list.length === 20,
      };
    },
    getNextPageParam: (lastPage) => {
      return lastPage.hasMore ? lastPage.page + 1 : undefined;
    },
    enabled: !!projectId,
    staleTime: 60 * 1000,
    refetchOnWindowFocus: false,
  });

  // Flatten all records from all pages
  const allRecords = data?.pages.flatMap((page) => page.records) || [];

  const handleSelectionChange = (newSelectedIds: string[]) => {
    setSelectedIds(newSelectedIds);
  };

  const handleSelectAll = () => {
    const allRecordIds = allRecords.map(
      (record) => record.observationId || record.id
    );
    setSelectedIds(allRecordIds);
  };

  const handleClearAll = () => {
    setSelectedIds([]);
  };

  const handleGenerateSummary = async () => {
    if (selectedIds.length === 0) {
      Toast.show({
        content: '请至少选择一条观察记录',
        position: 'center',
      });
      return;
    }

    // Validate selection count (reasonable limit)
    if (selectedIds.length > 50) {
      Toast.show({
        content: '一次最多选择50条观察记录',
        position: 'center',
      });
      return;
    }

    // Show confirmation for large selections
    if (selectedIds.length > 20) {
      const confirmed = await Dialog.confirm({
        content: `您选择了 ${selectedIds.length} 条观察记录，生成总结可能需要较长时间，是否继续？`,
        confirmText: '继续生成',
        cancelText: '取消',
      });

      if (!confirmed) {
        return;
      }
    }

    // 打开 AI 生成弹窗
    setShowAIModal(true);
  };

  const handleAIConfirm = async (title: string, summary: string) => {
    setIsGenerating(true);

    try {
      await createPeriodicSummary({
        projectId,
        observationIds: selectedIds,
        title,
        summary,
      });

      Toast.show({
        content: '总结生成成功！',
        position: 'center',
      });

      // Navigate back to summary list
      router.back();
    } catch (err) {
      console.error('Failed to generate summary:', err);

      // Show more specific error messages
      const errorMessage =
        err instanceof Error ? err.message : '生成总结失败，请重试';

      Toast.show({
        content: errorMessage.includes('网络')
          ? '网络连接失败，请检查网络后重试'
          : '生成总结失败，请重试',
        position: 'center',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleLoadMore = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  };

  const handleRetry = () => {
    window.location.reload();
  };

  const getButtonText = () => {
    if (isGenerating) {
      return '正在生成总结，请稍候...';
    }
    if (selectedIds.length > 0) {
      return `生成总结 (${selectedIds.length}条记录)`;
    }
    return '请先选择观察记录';
  };

  if (error) {
    return (
      <div className="flex min-h-screen flex-col">
        <NavBar onBack={() => router.back()}>选择观察记录</NavBar>
        <div className="flex flex-1 items-center justify-center p-4">
          <div className="text-center">
            <div className="mb-4">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
                <span className="text-2xl">⚠️</span>
              </div>
              <h3 className="mb-2 font-medium text-gray-900 text-lg">
                加载失败
              </h3>
              <p className="text-gray-500">
                无法加载观察记录，请检查网络连接后重试
              </p>
            </div>
            <Button className="mt-4" color="primary" onClick={handleRetry}>
              重新加载
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col bg-slate-50">
      {/* Sticky selection header */}
      <div className="sticky top-0 z-10 bg-white px-4 py-3 shadow-sm">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="font-semibold text-gray-900 text-lg">
              已选择 {selectedIds.length} 条记录
            </h2>
            {selectedIds.length > 0 && (
              <p className="mt-1 text-gray-500 text-xs">
                预计生成时间: {Math.ceil(selectedIds.length / 5)} 分钟
              </p>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              disabled={
                selectedIds.length === allRecords.length ||
                allRecords.length === 0
              }
              fill="outline"
              onClick={handleSelectAll}
              size="small"
            >
              全选
            </Button>
            <Button
              disabled={selectedIds.length === 0}
              fill="outline"
              onClick={handleClearAll}
              size="small"
            >
              清空
            </Button>
          </div>
        </div>
        {allRecords.length > 0 && (
          <p className="mt-2 text-gray-500 text-xs">
            共 {allRecords.length} 条可选记录
          </p>
        )}
      </div>

      {/* Records list with proper scrolling */}
      {/* Added bottom padding so content is not hidden behind the fixed action bar */}
      <div className="flex-1 space-y-6 overflow-y-auto px-4 py-2 pb-24">
        <RecordSelector
          hasMore={hasNextPage}
          isLoading={isLoading}
          isLoadingMore={isFetchingNextPage}
          onLoadMore={handleLoadMore}
          onSelectionChange={handleSelectionChange}
          records={allRecords}
          selectedIds={selectedIds}
        />
      </div>

      {/* Fixed bottom generate button with better styling */}
      {/* Make this bar fixed to the viewport bottom and ensure it overlays content */}
      <div className="fixed right-0 bottom-0 left-0 z-20 border-gray-200 border-t bg-white/95 p-4 backdrop-blur supports-[backdrop-filter]:bg-white/80">
        <Button
          block
          className="font-medium"
          color="primary"
          disabled={selectedIds.length === 0 || isGenerating}
          loading={isGenerating}
          onClick={handleGenerateSummary}
          size="large"
        >
          {getButtonText()}
        </Button>
      </div>

      {/* AI 生成总结弹窗 */}
      <AISummaryModal
        onClose={() => setShowAIModal(false)}
        onConfirm={handleAIConfirm}
        selectedRecords={allRecords.filter((record) =>
          selectedIds.includes(record.observationId || record.id)
        )}
        visible={showAIModal}
      />
    </div>
  );
}
