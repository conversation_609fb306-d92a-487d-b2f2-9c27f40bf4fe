'use client';

import { useQuery } from '@tanstack/react-query';
import { ErrorBlock, Skeleton } from 'antd-mobile';
import { FileText } from 'lucide-react';
import { getObservationList } from '@/api/pbl';
import RecordItem from '../../../../record/list/components/RecordItem';
import type { RecordData } from '../../../../record/list/mock/recordData';

interface RelatedRecordsListProps {
  observationIds: string[];
  projectId?: string;
  title?: string;
  className?: string;
}

export default function RelatedRecordsList({
  observationIds,
  projectId,
  title = '相关观察记录',
  className = '',
}: RelatedRecordsListProps) {
  const {
    data: records,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['relatedObservationRecords', observationIds],
    queryFn: async () => {
      if (!observationIds || observationIds.length === 0) {
        return [];
      }

      const response = await getObservationList({
        observationIds,
        projectId,
        page: 1,
        perPage: observationIds.length,
      });

      const responseData = response as unknown;
      let recordsList: RecordData[] = [];

      if (Array.isArray(responseData)) {
        recordsList = responseData as RecordData[];
      } else if (
        responseData &&
        typeof responseData === 'object' &&
        'list' in responseData &&
        Array.isArray((responseData as { list: RecordData[] }).list)
      ) {
        recordsList = (responseData as { list: RecordData[] }).list;
      }

      return recordsList;
    },
    enabled: !!(observationIds && observationIds.length > 0),
    staleTime: 5 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  if (!observationIds || observationIds.length === 0) {
    return null;
  }

  return (
    <div className={`m-4 rounded-xl bg-white shadow-sm ${className}`}>
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 text-lg">{title}</h3>
        <p className="mt-1 text-gray-500 text-sm">
          共 {observationIds.length} 条相关记录
        </p>
      </div>

      {isLoading && (
        <div className="space-y-4">
          {[1, 2, 3].map((id) => (
            <div className="rounded-2xl bg-gray-50 p-4" key={`skeleton-${id}`}>
              <Skeleton.Title animated />
              <Skeleton.Paragraph animated lineCount={2} />
            </div>
          ))}
        </div>
      )}

      {!!error && (
        <div className="py-8">
          <ErrorBlock
            description="无法加载相关观察记录，请稍后重试"
            status="disconnected"
            title="加载失败"
          />
        </div>
      )}

      {!(isLoading || error) && (!records || records.length === 0) && (
        <div className="py-8 text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
            <FileText className="h-6 w-6 text-gray-400" />
          </div>
          <h4 className="mb-1 font-medium text-gray-900">暂无相关记录</h4>
          <p className="text-gray-500 text-sm">未找到对应的观察记录</p>
        </div>
      )}

      {!(isLoading || error) && records && records.length > 0 && (
        <div className="space-y-3">
          {records.map((record) => (
            <div key={record.id || record.observationId}>
              <RecordItem record={record} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
