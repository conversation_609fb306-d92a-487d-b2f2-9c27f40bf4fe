'use client';

import { Dialog, Form, Input, TextArea, Toast } from 'antd-mobile';
import { Edit, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { deletePeriodicSummary, updatePeriodicSummary } from '@/api/pbl';
import type { SummaryDetailResponse } from '../../../types';

interface SummaryActionButtonsProps {
  summary: SummaryDetailResponse;
  onDelete?: () => void;
  onEdit?: () => void;
  onUpdate?: (updatedSummary: SummaryDetailResponse) => void;
}

interface EditFormData {
  title: string;
  summary: string;
}

export default function SummaryActionButtons({
  summary,
  onDelete,
  onEdit,
  onUpdate,
}: SummaryActionButtonsProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [editFormVisible, setEditFormVisible] = useState(false);
  const [form] = Form.useForm<EditFormData>();

  const handleDelete = () => {
    Dialog.confirm({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这个总结吗？',
      confirmText: '删除',
      cancelText: '取消',
      onConfirm: async () => {
        setIsDeleting(true);
        try {
          await deletePeriodicSummary(summary.summaryId);
          Toast.show({
            content: '删除成功',
            position: 'top',
          });
          onDelete?.();
        } catch (deleteError) {
          console.error('Failed to delete summary:', deleteError);
          Toast.show({
            content: '删除失败，请重试',
            position: 'top',
          });
        } finally {
          setIsDeleting(false);
        }
      },
    });
  };

  const handleEdit = () => {
    form.setFieldsValue({
      title: summary.title,
      summary: summary.summary,
    });
    setEditFormVisible(true);
    onEdit?.();
  };

  const handleSaveEdit = async () => {
    try {
      const values = await form.validateFields();
      setIsUpdating(true);

      const updateData = {
        title: values.title,
        summary: values.summary,
      };

      await updatePeriodicSummary(summary.summaryId, updateData);

      const updatedSummary: SummaryDetailResponse = {
        ...summary,
        title: values.title,
        summary: values.summary,
        updateTime: new Date().toISOString(),
      };

      Toast.show({
        content: '更新成功',
        position: 'top',
      });

      setEditFormVisible(false);
      onUpdate?.(updatedSummary);
    } catch (error) {
      console.error('Failed to update summary:', error);
      Toast.show({
        content: '更新失败，请重试',
        position: 'top',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancelEdit = () => {
    setEditFormVisible(false);
    form.resetFields();
  };

  return (
    <>
      <div
        className="fixed inset-x-0 bottom-0 z-50 border-gray-100 border-t bg-white px-4 py-4"
        style={{ paddingBottom: 'env(safe-area-inset-bottom)' }}
      >
        <div className="flex gap-3">
          <button
            className="flex flex-1 items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 transition-colors hover:bg-gray-50"
            onClick={handleEdit}
            type="button"
          >
            <Edit className="h-4 w-4" />
            编辑
          </button>
          <button
            className={`flex items-center justify-center gap-2 rounded-lg px-4 py-2 font-medium text-white transition-colors ${
              isDeleting
                ? 'cursor-not-allowed bg-gray-400'
                : 'bg-red-500 hover:bg-red-600'
            }`}
            disabled={isDeleting}
            onClick={handleDelete}
            type="button"
          >
            <Trash2 className="h-4 w-4" />
            {isDeleting ? '删除中...' : '删除'}
          </button>
        </div>
      </div>

      <Dialog
        closeOnMaskClick={false}
        content={
          <Form
            footer={
              <div className="flex gap-3">
                <button
                  className="flex-1 rounded-lg border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 transition-colors hover:bg-gray-50"
                  onClick={handleCancelEdit}
                  type="button"
                >
                  取消
                </button>
                <button
                  className={`flex-1 rounded-lg px-4 py-2 font-medium text-white transition-colors ${
                    isUpdating
                      ? 'cursor-not-allowed bg-gray-400'
                      : 'bg-blue-500 hover:bg-blue-600'
                  }`}
                  disabled={isUpdating}
                  onClick={handleSaveEdit}
                  type="button"
                >
                  {isUpdating ? '保存中...' : '保存'}
                </button>
              </div>
            }
            form={form}
            layout="vertical"
          >
            <Form.Item
              label="标题"
              name="title"
              rules={[
                { required: true, message: '请输入标题' },
                { max: 30, message: '标题不能超过30个字符' },
              ]}
            >
              <Input placeholder="请输入标题" />
            </Form.Item>
            <Form.Item
              label="总结内容"
              name="summary"
              rules={[
                { required: true, message: '请输入总结内容' },
                { min: 10, message: '总结内容至少10个字符' },
              ]}
            >
              <TextArea
                maxLength={2000}
                placeholder="请输入总结内容"
                rows={8}
                showCount
              />
            </Form.Item>
          </Form>
        }
        title="编辑总结"
        visible={editFormVisible}
      />
    </>
  );
}
