'use client';

import { But<PERSON>, Card, List, Space } from 'antd-mobile';
import Cookies from 'js-cookie';
import {
  Activity,
  ArrowRight,
  Bell,
  BookOpen,
  Calendar,
  Camera,
  CreditCard,
  FileText,
  MessageCircle,
  Music,
  Settings,
  Shield,
  Star,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ModuleItem {
  title: string;
  description: string;
  path: string;
  icon: React.ReactNode;
  color: string;
}

export default function HomePage() {
  Cookies.set('Authorization', 'ftz0z0sNftSUWO7zF5FYzvw5sr_oyUv5khqFXlncioo=');

  const router = useRouter();

  const coreModules: ModuleItem[] = [
    {
      title: 'PBL项目式学习',
      description: '项目创建、过程记录、材料管理、成果报告',
      path: '/pbl',
      icon: <BookOpen size={24} />,
      color: '#1890ff',
    },
    {
      title: 'PBL项目式学习V2',
      description: '升级版PBL学习系统',
      path: '/pbl-v2',
      icon: <BookOpen size={24} />,
      color: '#52c41a',
    },
    {
      title: '活动中心',
      description: '鲜花活动、暑期挑战、开学季专题、视频竞赛',
      path: '/activity',
      icon: <Activity size={24} />,
      color: '#fa541c',
    },
    {
      title: '电子表单',
      description: '表单创建、编辑和提交功能',
      path: '/form',
      icon: <FileText size={24} />,
      color: '#13c2c2',
    },
    {
      title: '考勤周报',
      description: '考勤记录和周报管理',
      path: '/attendance/weeklyReport',
      icon: <Calendar size={24} />,
      color: '#722ed1',
    },
    {
      title: '教育缴费',
      description: '学生缴费账单查询和管理',
      path: '/eduPay',
      icon: <CreditCard size={24} />,
      color: '#eb2f96',
    },
  ];

  const supportModules: ModuleItem[] = [
    {
      title: '体检管理',
      description: '学生体检记录和健康管理',
      path: '/checkup',
      icon: <Shield size={24} />,
      color: '#f5222d',
    },
    {
      title: '亲子任务',
      description: '家园共育任务管理',
      path: '/babyTask',
      icon: <Users size={24} />,
      color: '#faad14',
    },
    {
      title: '成长相册',
      description: '记录孩子成长的美好时光',
      path: '/album',
      icon: <Camera size={24} />,
      color: '#a0d911',
    },
    {
      title: '通知公告',
      description: '重要通知和公告发布',
      path: '/notice',
      icon: <Bell size={24} />,
      color: '#1890ff',
    },
    {
      title: '食谱管理',
      description: '营养食谱制定和管理',
      path: '/recipe',
      icon: <Star size={24} />,
      color: '#52c41a',
    },
    {
      title: '安全巡查',
      description: '校园安全巡查记录',
      path: '/securityPatrol',
      icon: <Shield size={24} />,
      color: '#fa541c',
    },
  ];

  const additionalModules: ModuleItem[] = [
    {
      title: '协作空间',
      description: '团队协作和交流平台',
      path: '/collaboration',
      icon: <MessageCircle size={24} />,
      color: '#13c2c2',
    },
    {
      title: '接送管理',
      description: '学生接送安全管理',
      path: '/waitingForPickup',
      icon: <Users size={24} />,
      color: '#722ed1',
    },
    {
      title: '调研项目',
      description: '问卷调研和数据分析',
      path: '/surveyProject',
      icon: <FileText size={24} />,
      color: '#eb2f96',
    },
    {
      title: '云桌面',
      description: '云端办公和资源管理',
      path: '/cloudDesk',
      icon: <Settings size={24} />,
      color: '#f5222d',
    },
    {
      title: '直播间',
      description: '在线直播和互动教学',
      path: '/live',
      icon: <Music size={24} />,
      color: '#faad14',
    },
    {
      title: '服务中心',
      description: '客户服务和帮助中心',
      path: '/serviceCenter',
      icon: <Settings size={24} />,
      color: '#a0d911',
    },
  ];

  const handleModuleClick = (path: string) => {
    router.push(path);
  };

  const renderModuleCard = (modules: ModuleItem[], title: string) => (
    <Card
      style={{
        margin: '16px 0',
        borderRadius: '12px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      }}
      title={
        <div
          style={{
            color: '#1890ff',
            fontSize: '18px',
            fontWeight: 'bold',
            borderBottom: '2px solid #1890ff',
            paddingBottom: '8px',
            marginBottom: '16px',
          }}
        >
          {title}
        </div>
      }
    >
      <List>
        {modules.map((module, index) => (
          <List.Item
            className="transition-colors hover:bg-blue-50"
            extra={<ArrowRight color="#999" size={16} />}
            key={index}
            onClick={() => handleModuleClick(module.path)}
            prefix={
              <div
                style={{
                  color: module.color,
                  backgroundColor: `${module.color}15`,
                  padding: '8px',
                  borderRadius: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                {module.icon}
              </div>
            }
            style={{
              padding: '16px 12px',
              cursor: 'pointer',
              borderRadius: '8px',
              margin: '4px 0',
            }}
          >
            <div>
              <div
                style={{
                  fontSize: '16px',
                  fontWeight: '500',
                  color: '#1890ff',
                  marginBottom: '4px',
                }}
              >
                {module.title}
              </div>
              <div
                style={{
                  fontSize: '14px',
                  color: '#666',
                  lineHeight: '1.4',
                }}
              >
                {module.description}
              </div>
            </div>
          </List.Item>
        ))}
      </List>
    </Card>
  );

  return (
    <div
      style={{
        padding: '16px',
        backgroundColor: '#f5f5f5',
        minHeight: '100vh',
      }}
    >
      <div
        style={{
          textAlign: 'center',
          marginBottom: '24px',
          padding: '20px',
          background: 'linear-gradient(135deg, #1890ff, #40a9ff)',
          borderRadius: '12px',
          color: 'white',
        }}
      >
        <h1
          style={{
            fontSize: '24px',
            margin: '0 0 8px 0',
            fontWeight: 'bold',
          }}
        >
          功能模块导航
        </h1>
        <p
          style={{
            fontSize: '14px',
            margin: 0,
            opacity: 0.9,
          }}
        >
          探索应用的核心功能和服务
        </p>
      </div>

      <Space direction="vertical" style={{ width: '100%' }}>
        {renderModuleCard(coreModules, '🎯 核心功能模块')}
        {renderModuleCard(supportModules, '🛠️ 支持功能模块')}
        {renderModuleCard(additionalModules, '⚡ 扩展功能模块')}
      </Space>

      <div
        style={{
          textAlign: 'center',
          marginTop: '32px',
          padding: '16px',
          color: '#666',
          fontSize: '12px',
        }}
      >
        <p>点击任意模块卡片即可跳转到对应功能页面</p>
        <p style={{ margin: '8px 0 0 0' }}>
          总计{' '}
          {coreModules.length +
            supportModules.length +
            additionalModules.length}{' '}
          个功能模块
        </p>
      </div>
    </div>
  );
}
