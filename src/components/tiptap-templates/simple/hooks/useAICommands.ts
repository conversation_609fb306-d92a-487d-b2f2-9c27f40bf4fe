import type { Editor } from '@tiptap/core';
import React from 'react';
import {
  hasActivePreview,
  hideAIPreview,
  showAIPreview,
  updateAIPreview,
} from '@/components/tiptap-templates/simple/plugins/ai-preview-plugin';
import {
  tiptapToMarkdown,
  tiptapToMarkdownWithPreciseCursor,
} from '@/components/tiptap-templates/simple/tiptap-markdown-converter';
import {
  AI_CONTENT_TAG,
  AI_OUTPUT_INSTRUCTION,
  AI_SYSTEM_PROMPT,
} from '../constants';
import type { AICommandType } from '../types';

export function useAICommands(editor: Editor | null) {
  const [aiLoading, setAiLoading] = React.useState(false);

  // AI 命令处理函数
  const handleAICommand = React.useCallback(
    async (command: AICommandType, options?: Record<string, unknown>) => {
      if (!editor || aiLoading) {
        return;
      }

      const { from, to, empty } = editor.state.selection;
      const selectedText = empty
        ? ''
        : editor.state.doc.textBetween(from, to, ' ');
      console.log('🚀 ~ selectedText:', selectedText);

      // 获取完整文档内容
      const fullDocumentJSON = editor.getJSON();
      console.log('🚀 ~ fullDocumentJSON:', fullDocumentJSON);

      // 根据命令类型决定是否使用带光标位置的 markdown
      let fullDocumentMarkdown: string;

      if (command === 'continue' && empty) {
        // 续写命令且没有选中文本时，使用带光标位置标记的 markdown
        const cursorPosition = from;
        fullDocumentMarkdown = tiptapToMarkdownWithPreciseCursor(
          fullDocumentJSON,
          cursorPosition,
          editor
        );
      } else {
        // 其他情况使用常规 markdown
        fullDocumentMarkdown = tiptapToMarkdown(fullDocumentJSON);
      }
      console.log('🚀 ~ fullDocumentMarkdown:', fullDocumentMarkdown);

      setAiLoading(true);

      try {
        // 构建AI命令的查询内容
        let query = '';
        switch (command) {
          case 'continue':
            query = `${AI_SYSTEM_PROMPT}

[重要规则]
- 只返回续写的内容
- 根据上下文自然续写
- 保持语言风格一致
- 续写长度适中（50-300字）

[全文内容]
<full_text>
${fullDocumentMarkdown}
</full_text>

[任务]
在${AI_CONTENT_TAG}位置续写内容，使文章自然连贯。${AI_OUTPUT_INSTRUCTION}`;
            break;
          case 'summarize':
            query = selectedText
              ? `请处理以下内容：

              ${selectedText}

              请补充标签"${AI_CONTENT_TAG}"处的内容，用“总结”的方式。${AI_OUTPUT_INSTRUCTION}`
              : `请总结以下文档内容：

            ${fullDocumentMarkdown}

            请生成标签"${AI_CONTENT_TAG}"合适的内容。${AI_OUTPUT_INSTRUCTION}`;
            break;
          case 'polish':
            query = selectedText
              ? `请润色以下内容，使其更加流畅和专业：

                ${selectedText}

                请生成标签"${AI_CONTENT_TAG}"合适的内容。${AI_OUTPUT_INSTRUCTION}`
              : `请润色以下文档内容，使其更加流畅和专业：

            ${fullDocumentMarkdown}

            请生成标签"${AI_CONTENT_TAG}"合适的内容。${AI_OUTPUT_INSTRUCTION}`;
            break;
          case 'expand':
            query = selectedText
              ? `请扩写以下内容，增加更多细节和深度：

              ${selectedText}

              请生成标签"${AI_CONTENT_TAG}"合适的内容。${AI_OUTPUT_INSTRUCTION}`
              : `请扩写以下文档内容，增加更多细节和深度：

              ${fullDocumentMarkdown}
              ${AI_CONTENT_TAG}

              请生成标签"${AI_CONTENT_TAG}"合适的内容。${AI_OUTPUT_INSTRUCTION}`;
            break;
          case 'shorten':
            query = `请精简以下内容，保留核心信息：

            ${selectedText}

            请生成标签"${AI_CONTENT_TAG}"合适的内容。${AI_OUTPUT_INSTRUCTION}`;
            break;
          case 'adjustTone': {
            const tone = options?.tone || 'professional';
            const toneMap: Record<string, string> = {
              formal: '正式',
              casual: '轻松',
              professional: '专业',
              friendly: '友好',
            };
            query = `请将以下内容调整为${toneMap[tone as string] || '专业'}的语气：

            ${selectedText}

            请生成标签"${AI_CONTENT_TAG}"合适的内容。${AI_OUTPUT_INSTRUCTION}`;
            break;
          }
          default:
            query = `${selectedText || fullDocumentMarkdown}
            ${AI_CONTENT_TAG}

            请生成标签"${AI_CONTENT_TAG}"合适的内容。${AI_OUTPUT_INSTRUCTION}`;
        }

        const response = await fetch('/api/message', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ message: query }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 如果已有预览，先隐藏
        if (hasActivePreview(editor)) {
          hideAIPreview(editor);
        }

        // 确定插入位置
        let insertPosition: number;

        if (command === 'continue') {
          insertPosition = from;
        } else if (empty) {
          insertPosition = from;
        } else {
          insertPosition = from;
          // 对于替换类型的命令，不立即删除选中内容，而是在用户接受预览后再处理
        }

        const reader = response.body?.getReader();
        const decoder = new TextDecoder();

        if (reader) {
          let buffer = '';
          let accumulatedContent = '';
          let isFirstChunk = true; // 标记是否是第一个数据块

          while (true) {
            const { done, value } = await reader.read();

            if (done) {
              break;
            }

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);

                if (data === '[DONE]') {
                  break;
                }

                try {
                  const parsed = JSON.parse(data);
                  if (parsed.content) {
                    // 累积内容
                    accumulatedContent += parsed.content;

                    // 调试日志
                    console.log('🔥 流式内容累积:', {
                      chunk: parsed.content,
                      accumulated: accumulatedContent,
                      length: accumulatedContent.length,
                      isFirstChunk,
                    });

                    // 使用预览功能显示累积的内容
                    if (accumulatedContent.trim()) {
                      if (isFirstChunk) {
                        // 第一次：显示新预览
                        console.log('🎆 第一次流式数据，调用 showAIPreview');
                        showAIPreview(
                          editor,
                          accumulatedContent,
                          insertPosition
                        );
                        isFirstChunk = false;
                      } else {
                        // 后续：更新现有预览
                        console.log('🔄 后续流式数据，调用 updateAIPreview');
                        updateAIPreview(
                          editor,
                          accumulatedContent,
                          insertPosition
                        );
                      }
                    }
                  }
                } catch (e) {
                  console.error('解析流数据错误:', e);
                }
              }
            }
          }
        }
      } catch (error: unknown) {
        console.error('AI Command error:', error);

        // 错误时显示错误预览
        const errorMessage = `AI 操作失败: ${error instanceof Error ? error.message : '未知错误'}`;
        showAIPreview(editor, errorMessage, from);
      } finally {
        setAiLoading(false);
      }
    },
    [editor, aiLoading]
  );

  // 处理自定义提示词
  const handleCustomPrompt = React.useCallback(
    async (prompt: string) => {
      if (!editor || aiLoading || !prompt.trim()) {
        return;
      }

      // 如果已有预览，先隐藏
      if (hasActivePreview(editor)) {
        hideAIPreview(editor);
      }

      // 获取完整文档内容作为上下文
      const fullDocumentJSON = editor.getJSON();
      const { from } = editor.state.selection;

      // 为自定义提示词也使用带光标位置的 markdown，让 AI 了解上下文
      const fullDocumentMarkdown = tiptapToMarkdownWithPreciseCursor(
        fullDocumentJSON,
        from,
        editor
      );

      setAiLoading(true);
      const insertPosition = from;

      try {
        // 构建完整的查询内容,包含用户提示词和文档上下文
        const query = `用户请求：${prompt}

        ${fullDocumentMarkdown}

        请根据用户的请求和文档上下文，生成标签"${AI_CONTENT_TAG}"合适的内容。${AI_OUTPUT_INSTRUCTION}`;

        const response = await fetch('/api/message', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ message: query }),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        const decoder = new TextDecoder();

        if (reader) {
          let buffer = '';
          let accumulatedContent = '';
          let isFirstChunk = true; // 标记是否是第一个数据块

          while (true) {
            const { done, value } = await reader.read();

            if (done) {
              break;
            }

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);

                if (data === '[DONE]') {
                  break;
                }

                try {
                  const parsed = JSON.parse(data);
                  if (parsed.content) {
                    // 累积内容
                    accumulatedContent += parsed.content;

                    // 调试日志
                    console.log('🔥 自定义提示词流式内容累积:', {
                      chunk: parsed.content,
                      accumulated: accumulatedContent,
                      length: accumulatedContent.length,
                      isFirstChunk,
                    });

                    // 使用预览功能显示累积的内容
                    if (accumulatedContent.trim()) {
                      if (isFirstChunk) {
                        // 第一次：显示新预览
                        console.log(
                          '🎆 自定义提示词第一次流式数据，调用 showAIPreview'
                        );
                        showAIPreview(
                          editor,
                          accumulatedContent,
                          insertPosition
                        );
                        isFirstChunk = false;
                      } else {
                        // 后续：更新现有预览
                        console.log(
                          '🔄 自定义提示词后续流式数据，调用 updateAIPreview'
                        );
                        updateAIPreview(
                          editor,
                          accumulatedContent,
                          insertPosition
                        );
                      }
                    }
                  }
                } catch (e) {
                  console.error('解析流数据错误:', e);
                }
              }
            }
          }
        }
      } catch (error: unknown) {
        console.error('Custom prompt error:', error);

        // 错误时显示错误预览
        const errorMessage = `AI 生成失败: ${error instanceof Error ? error.message : '未知错误'}`;
        showAIPreview(editor, errorMessage, insertPosition);
      } finally {
        setAiLoading(false);
      }
    },
    [editor, aiLoading]
  );

  return {
    aiLoading,
    handleAICommand,
    handleCustomPrompt,
  };
}
