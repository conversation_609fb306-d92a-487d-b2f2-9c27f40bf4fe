import type { J<PERSON><PERSON>ontent } from '@tiptap/core';
import { generateHTML, generateText } from '@tiptap/core';
import { Highlight } from '@tiptap/extension-highlight';
import { Image } from '@tiptap/extension-image';
import { TaskItem, TaskList } from '@tiptap/extension-list';
import { Subscript } from '@tiptap/extension-subscript';
import { Superscript } from '@tiptap/extension-superscript';
import { TextAlign } from '@tiptap/extension-text-align';
import { Typography } from '@tiptap/extension-typography';
import { StarterKit } from '@tiptap/starter-kit';
import { renderToHTMLString, renderToMarkdown } from '@tiptap/static-renderer';
import { AI_CONTENT_TAG } from '@/components/tiptap-templates/simple/constants';

// 配置与编辑器相同的扩展
const extensions = [
  StarterKit.configure({
    // horizontalRule: false,
    link: {
      openOnClick: false,
      enableClickSelection: true,
    },
  }),
  TextAlign.configure({ types: ['heading', 'paragraph'] }),
  TaskList,
  TaskItem.configure({ nested: true }),
  Highlight.configure({ multicolor: true }),
  Image,
  Typography,
  Superscript,
  Subscript,
];

// 定义节点类型接口
interface TiptapNode {
  type?: string;
  text?: string;
  content?: TiptapNode[];
  [key: string]: unknown;
}

/**
 * 将 Tiptap JSON 内容转换为纯文本
 */
export function tiptapToText(content: JSONContent): string {
  try {
    return generateText(content, extensions);
  } catch (error) {
    console.error('Failed to convert Tiptap content to text:', error);
    return '';
  }
}

/**
 * 将 Tiptap JSON 内容转换为 HTML
 */
export function tiptapToHTML(content: JSONContent): string {
  try {
    return renderToHTMLString({
      extensions,
      content,
    });
  } catch (error) {
    console.error('Failed to convert Tiptap content to HTML:', error);
    // 降级到 generateHTML
    try {
      return generateHTML(content, extensions);
    } catch (fallbackError) {
      console.error('Fallback HTML generation also failed:', fallbackError);
      return '';
    }
  }
}

/**
 * 将 Tiptap JSON 内容转换为 Markdown
 * 使用官方的 @tiptap/static-renderer
 */
export function tiptapToMarkdown(content: JSONContent): string {
  try {
    return renderToMarkdown({
      extensions,
      content,
    });
  } catch (error) {
    console.error('Failed to convert Tiptap content to markdown:', error);
    // 降级到纯文本
    return tiptapToText(content);
  }
}

/**
 * 将 Tiptap JSON 内容转换为带光标位置标记的 Markdown
 * 在光标位置插入 [CURSOR] 标记，让 AI 知道续写位置
 */
export function tiptapToMarkdownWithCursor(
  content: JSONContent,
  cursorPosition: number
): string {
  try {
    // 先获取纯文本版本以计算字符位置
    const plainText = tiptapToText(content);

    // 如果光标位置超出文档长度，则在末尾添加标记
    const adjustedPosition = Math.min(cursorPosition, plainText.length);

    // 获取 markdown 版本
    const markdown = renderToMarkdown({
      extensions,
      content,
    });

    // 简单的方法：在文档末尾添加光标位置信息
    // 更精确的方法需要深入解析 ProseMirror 文档结构
    if (adjustedPosition >= plainText.length) {
      return `${markdown}\n\n${AI_CONTENT_TAG}`;
    }
    if (adjustedPosition === 0) {
      return `${AI_CONTENT_TAG}\n\n${markdown}`;
    }
    // 对于中间位置，使用简单的占位符
    return `${markdown}\n\n${AI_CONTENT_TAG}`;
  } catch (error) {
    console.error(
      'Failed to convert Tiptap content to markdown with cursor:',
      error
    );
    return tiptapToMarkdown(content);
  }
}

/**
 * 更精确的光标位置标记方法
 * 通过在编辑器中临时插入标记，生成 markdown 后删除标记
 */
export function tiptapToMarkdownWithPreciseCursor(
  content: JSONContent,
  cursorPosition: number,
  editor?: any // 可选的编辑器实例，用于临时插入标记
): string {
  // 如果有编辑器实例，使用临时插入标记的方法
  if (editor) {
    return tiptapToMarkdownWithTemporaryMarker(editor, cursorPosition);
  }

  // 否则使用原有的递归方法（作为备选方案）
  try {
    // 创建一个深拷贝来避免修改原始内容
    const contentWithCursor = JSON.parse(JSON.stringify(content));

    // 插入光标标记的递归函数
    function insertCursorMark(node: TiptapNode, currentPos: number): number {
      if (!node || typeof node !== 'object') {
        return currentPos;
      }

      if (node.type === 'text' && node.text) {
        const textLength = node.text.length;
        if (
          currentPos <= cursorPosition &&
          currentPos + textLength >= cursorPosition
        ) {
          // 光标在这个文本节点内
          const relativePos = cursorPosition - currentPos;
          const beforeText = node.text.substring(0, relativePos);
          const afterText = node.text.substring(relativePos);
          node.text = `${beforeText}${AI_CONTENT_TAG}${afterText}`;
        }
        return currentPos + textLength;
      }

      let position = currentPos;
      if (node.content && Array.isArray(node.content)) {
        for (const child of node.content) {
          position = insertCursorMark(child, position);
          if (position > cursorPosition) {
            break;
          }
        }
      }

      return position;
    }

    // 插入光标标记
    insertCursorMark(contentWithCursor, 0);
    console.log('🚀 ~ contentWithCursor:', contentWithCursor);

    // 转换为 markdown
    return renderToMarkdown({
      extensions,
      content: contentWithCursor,
    });
  } catch (error) {
    console.error(
      'Failed to convert Tiptap content to markdown with precise cursor:',
      error
    );
    return tiptapToMarkdownWithCursor(content, cursorPosition);
  }
}

/**
 * 使用临时标记的方法生成带光标位置的 markdown
 * 这是推荐的方法，因为它能准确反映光标在编辑器中的实际位置
 */
export function tiptapToMarkdownWithTemporaryMarker(
  editor: any,
  cursorPosition: number
): string {
  try {
    // 保存当前选择状态

    // 在光标位置插入临时标记
    editor
      .chain()
      .focus()
      .insertContentAt(cursorPosition, AI_CONTENT_TAG)
      .run();

    // 获取带标记的内容并转换为 markdown
    const contentWithMarker = editor.getJSON();
    const markdownWithMarker = renderToMarkdown({
      extensions,
      content: contentWithMarker,
    });

    // 删除临时标记，恢复原始内容
    // 查找标记在文档中的位置
    const markerStart = cursorPosition;
    const markerEnd = cursorPosition + AI_CONTENT_TAG.length;

    // 删除标记
    editor
      .chain()
      .focus()
      .deleteRange({ from: markerStart, to: markerEnd })
      .run();

    // 恢复原始光标位置
    editor.chain().focus().setTextSelection(cursorPosition).run();

    console.log('📝 使用临时标记生成的 Markdown:', markdownWithMarker);

    return markdownWithMarker;
  } catch (error) {
    console.error('Failed to generate markdown with temporary marker:', error);
    // 如果出错，尝试恢复编辑器状态
    try {
      // 尝试删除可能残留的标记
      const currentContent = editor.getHTML();
      if (currentContent.includes(AI_CONTENT_TAG)) {
        const doc = editor.state.doc;
        let markerPos = -1;

        // 查找标记位置
        doc.descendants((node: any, pos: number) => {
          if (node.isText && node.text?.includes(AI_CONTENT_TAG)) {
            const textContent = node.text;
            const markerIndex = textContent.indexOf(AI_CONTENT_TAG);
            if (markerIndex !== -1) {
              markerPos = pos + markerIndex;
              return false; // 停止遍历
            }
          }
        });

        // 如果找到标记，删除它
        if (markerPos !== -1) {
          editor
            .chain()
            .focus()
            .deleteRange({
              from: markerPos,
              to: markerPos + AI_CONTENT_TAG.length,
            })
            .run();
        }
      }
    } catch (cleanupError) {
      console.error('Failed to cleanup marker:', cleanupError);
    }

    // 降级到原有方法
    return tiptapToMarkdownWithCursor(editor.getJSON(), cursorPosition);
  }
}
